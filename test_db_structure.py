#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json

def test_ai_settings_structure():
    """اختبار بنية جدول ai_settings"""
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        # التحقق من بنية الجدول
        cursor.execute("PRAGMA table_info(ai_settings)")
        columns = cursor.fetchall()
        
        print("=== بنية جدول ai_settings ===")
        for i, col in enumerate(columns):
            print(f"فهرس {i}: {col[1]} ({col[2]})")
        
        # إنشاء بيانات اختبار
        test_button_id = 999
        test_api_keys = json.dumps(["test_key_1", "test_key_2"])
        test_model = "gemma-3n-e2b-it"
        test_base_prompt = "أنت مساعد اختبار"
        test_identity = "أنا مساعد اختبار"
        test_start_message = "مرحباً، هذا اختبار"
        test_allow_images = 1
        
        # حذف البيانات القديمة إن وجدت
        cursor.execute('DELETE FROM ai_settings WHERE button_id = ?', (test_button_id,))
        
        # إدراج بيانات اختبار
        cursor.execute('''
            INSERT INTO ai_settings 
            (button_id, api_keys, model, base_prompt, identity_override, start_message, allow_images)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (test_button_id, test_api_keys, test_model, test_base_prompt, test_identity, test_start_message, test_allow_images))
        
        conn.commit()
        
        # جلب البيانات واختبار الفهارس
        cursor.execute('SELECT * FROM ai_settings WHERE button_id = ?', (test_button_id,))
        result = cursor.fetchone()
        
        if result:
            print("\n=== اختبار الفهارس ===")
            print(f"button_id (فهرس 0): {result[0]}")
            print(f"api_keys (فهرس 1): {result[1]}")
            print(f"model (فهرس 2): {result[2]}")
            print(f"base_prompt (فهرس 3): {result[3]}")
            print(f"identity_override (فهرس 4): {result[4]}")
            print(f"start_message (فهرس 5): {result[5]}")
            print(f"allow_images (فهرس 6): {result[6]}")
            
            # اختبار أنواع البيانات
            print(f"\nنوع start_message: {type(result[5])}")
            print(f"نوع allow_images: {type(result[6])}")
        
        # تنظيف البيانات
        cursor.execute('DELETE FROM ai_settings WHERE button_id = ?', (test_button_id,))
        conn.commit()
        
        conn.close()
        print("\n✅ اختبار بنية قاعدة البيانات مكتمل")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    test_ai_settings_structure()
