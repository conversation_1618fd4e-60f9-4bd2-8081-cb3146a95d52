#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3

def check_database():
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        print("=== أزرار الذكاء الاصطناعي الموجودة ===")
        cursor.execute('SELECT * FROM buttons WHERE type = "ذكاء صناعي"')
        ai_buttons = cursor.fetchall()
        if ai_buttons:
            for button in ai_buttons:
                print(f"الزر: {button[1]} (ID: {button[0]})")
        else:
            print("لا توجد أزرار ذكاء اصطناعي")
        
        print("\n=== إعدادات الذكاء الاصطناعي ===")
        cursor.execute('SELECT * FROM ai_settings')
        ai_settings = cursor.fetchall()
        if ai_settings:
            for setting in ai_settings:
                print(f"إعدادات للزر ID: {setting[0]}")
        else:
            print("لا توجد إعدادات ذكاء اصطناعي")
        
        print("\n=== جميع الأزرار ===")
        cursor.execute('SELECT id, label, type FROM buttons')
        all_buttons = cursor.fetchall()
        for button in all_buttons:
            print(f"ID: {button[0]}, الاسم: {button[1]}, النوع: {button[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"خطأ: {e}")

if __name__ == "__main__":
    check_database()
