# 🤖 دليل نظام الذكاء الاصطناعي المخصص

## 📋 نظرة عامة

تم تنفيذ نظام ذكاء اصطناعي مخصص متقدم في بوت تيليجرام باستخدام Google Gemini API، مع إمكانيات تخصيص شاملة للأدمن.

## ✅ الميزات المنفذة

### 🔧 إعدادات قابلة للتخصيص
- **مفاتيح API متعددة**: دعم أكثر من مفتاح مع توزيع ذكي
- **اختيار النموذج**: 3 نماذج مجانية من Google Gemini
- **البرومبت المخصص**: تعليمات مخصصة لتوجيه الذكاء
- **هوية مخصصة**: تحديد كيفية تعريف الذكاء لنفسه
- **رسالة البداية**: رسالة ترحيب قابلة للتخصيص
- **دعم الصور**: تفعيل/إلغاء استقبال الصور

### 🧠 النماذج المدعومة
1. **Gemma 3n E2B** (`gemma-3n-e2b-it`) - نص فقط
2. **Gemma 3 12B** (`gemma-3-12b-it`) - نص فقط
3. **Gemma 3 27B** (`gemma-3-27b-it`) - **نص + صور** 🖼️
4. **LearnLM 2 Flash** (`learnlm-2.0-flash-experimental`) - نص فقط

### 🎯 الهاشتاجات المدعومة
جميع الهاشتاجات الديناميكية متاحة في:
- البرومبت الأساسي
- هوية الذكاء
- رسالة البداية
- ردود الذكاء

الهاشتاجات المتاحة:
- `#id` - معرف المستخدم
- `#username` - اسم المستخدم
- `#name` - الاسم الكامل
- `#points` - عدد النقاط
- `#invitelink` - رابط الدعوة

## 🚀 كيفية الاستخدام

### للأدمن - إنشاء زر ذكاء اصطناعي

1. **بدء الإنشاء**:
   - اضغط على `END | +` أو `N | +`
   - أدخل اسم الزر
   - اختر نوع "ذكاء صناعي"

2. **عملية الإعداد (6 خطوات)**:

   **الخطوة 1: مفاتيح API**
   ```
   أدخل مفاتيح Google Gemini API مفصولة بفواصل:
   key1,key2,key3
   
   أو اكتب "تخطي" لاستخدام المفاتيح العامة
   ```

   **الخطوة 2: اختيار النموذج**
   - اختر من القائمة المعروضة

   **الخطوة 3: البرومبت الأساسي**
   ```
   مثال: "أنت مساعد طبي متخصص. لا تجيب إلا على الأسئلة الطبية."
   ```

   **الخطوة 4: هوية الذكاء**
   ```
   مثال: "أنا مساعد ذكي طوّره #name"
   ```

   **الخطوة 5: رسالة البداية**
   ```
   مثال: "مرحباً #name! أنا مساعدك الطبي الذكي."
   ```

   **الخطوة 6: دعم الصور**
   - **ملاحظة**: دعم الصور متاح فقط مع نموذج `gemma-3-27b-it`
   - إذا اخترت نموذج آخر، سيتم تخطي هذه الخطوة تلقائياً
   - إذا اخترت `gemma-3-27b-it`، اختر "نعم" أو "لا"

### للمستخدمين - استخدام الذكاء الاصطناعي

1. **بدء المحادثة**:
   - اضغط على زر الذكاء الاصطناعي
   - اقرأ رسالة البداية
   - اضغط على "🚀 بدء المحادثة"

2. **المحادثة**:
   - أرسل أسئلتك أو رسائلك
   - أرسل صور (إذا كان مفعل)
   - احصل على ردود فورية

3. **إنهاء المحادثة**:
   - اضغط على "🔚 إنهاء المحادثة" أسفل أي رد

## ⚙️ الإعدادات التقنية

### متغيرات البيئة (.env)
```env
BOT_TOKEN=your_bot_token
ADMIN_ID=your_admin_id
GEMINI_API_KEYS=key1,key2,key3
```

### قاعدة البيانات
جدول `ai_settings`:
- `button_id` - معرف الزر
- `api_keys` - مفاتيح API (JSON)
- `model` - النموذج المختار
- `base_prompt` - البرومبت الأساسي
- `identity_override` - هوية الذكاء
- `start_message` - رسالة البداية
- `allow_images` - دعم الصور (0/1)

## 🔧 معالجة الأخطاء

### توزيع المفاتيح الذكي
- التوزيع الدائري بين المفاتيح
- التبديل التلقائي عند فشل مفتاح
- رسائل خطأ واضحة

### معالجة الصور
- التحقق من دعم الصور قبل المعالجة
- تحويل الصور إلى Base64
- رسائل خطأ مفيدة

### إدارة الجلسات
- تتبع الجلسات في الذاكرة
- تنظيف تلقائي عند الإنهاء
- حماية من الجلسات المعلقة

## 🎨 واجهة المستخدم

### أزرار تفاعلية
- زر بدء المحادثة
- زر إنهاء المحادثة
- رسائل حالة واضحة

### رسائل ديناميكية
- دعم كامل للهاشتاجات
- رسائل مخصصة لكل مستخدم
- تحديث فوري للبيانات

## 📊 إحصائيات الاستخدام

### تتبع المحادثات
- حفظ تاريخ المحادثة في الجلسة
- تتبع استخدام المفاتيح
- إحصائيات الأداء

## 🔒 الأمان

### حماية المفاتيح
- تشفير المفاتيح في قاعدة البيانات
- عدم عرض المفاتيح في الرسائل
- تدوير تلقائي للمفاتيح

### صلاحيات المستخدمين
- إعداد الذكاء للأدمن فقط
- استخدام الذكاء لجميع المستخدمين
- حماية من الاستخدام المفرط

## 🚀 التطوير المستقبلي

### ميزات مقترحة
- حفظ المحادثات في قاعدة البيانات
- إحصائيات تفصيلية للاستخدام
- دعم المزيد من النماذج
- تخصيص متقدم للردود

### تحسينات الأداء
- تحسين استهلاك الذاكرة
- تحسين سرعة الاستجابة
- تحسين معالجة الأخطاء

## 📞 الدعم

للمساعدة أو الإبلاغ عن مشاكل:
- تحقق من سجلات الأخطاء
- راجع إعدادات المفاتيح
- تأكد من صحة النموذج المختار

---

**تم التطوير بواسطة**: Karrar Alhdarwi (كرار الحدراوي)
**التاريخ**: 2025-07-15
**الإصدار**: 1.0
