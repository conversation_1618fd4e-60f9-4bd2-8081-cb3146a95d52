#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الذكاء الاصطناعي المخصص
"""

import sqlite3
import json
import os
from keyboards.builder import (
    init_database, save_ai_settings, get_ai_settings, 
    delete_ai_settings, send_ai_request, render_ai_message
)

def test_database_setup():
    """اختبار إعداد قاعدة البيانات"""
    print("🔧 اختبار إعداد قاعدة البيانات...")
    
    try:
        # تهيئة قاعدة البيانات
        init_database()
        
        # التحقق من وجود جدول ai_settings
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='ai_settings'")
        result = cursor.fetchone()
        
        if result:
            print("✅ جدول ai_settings موجود")
        else:
            print("❌ جدول ai_settings غير موجود")
            return False
        
        # التحقق من أعمدة الجدول
        cursor.execute("PRAGMA table_info(ai_settings)")
        columns = cursor.fetchall()
        
        expected_columns = ['button_id', 'api_keys', 'model', 'base_prompt', 'identity_override', 'start_message', 'allow_images']
        actual_columns = [col[1] for col in columns]
        
        for col in expected_columns:
            if col in actual_columns:
                print(f"✅ العمود {col} موجود")
            else:
                print(f"❌ العمود {col} غير موجود")
                return False
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def test_ai_settings_crud():
    """اختبار عمليات CRUD لإعدادات الذكاء الاصطناعي"""
    print("\n📝 اختبار عمليات إدارة الإعدادات...")
    
    try:
        # بيانات اختبار
        test_button_id = 999
        test_api_keys = json.dumps(["test_key_1", "test_key_2"])
        test_model = "gemma-3n-e2b-it"
        test_base_prompt = "أنت مساعد اختبار"
        test_identity = "أنا مساعد اختبار"
        test_start_message = "مرحباً، هذا اختبار"
        test_allow_images = 1
        
        # اختبار الحفظ
        save_ai_settings(
            test_button_id, test_api_keys, test_model, 
            test_base_prompt, test_identity, test_start_message, test_allow_images
        )
        print("✅ تم حفظ الإعدادات")
        
        # اختبار الجلب
        settings = get_ai_settings(test_button_id)
        if settings:
            print("✅ تم جلب الإعدادات")
            print(f"   - النموذج: {settings[2]}")
            print(f"   - البرومبت: {settings[3]}")
            print(f"   - دعم الصور: {settings[7]}")
        else:
            print("❌ فشل في جلب الإعدادات")
            return False
        
        # اختبار التحديث
        save_ai_settings(
            test_button_id, test_api_keys, "gemma-3-12b-it", 
            "برومبت محدث", test_identity, test_start_message, 0
        )
        
        updated_settings = get_ai_settings(test_button_id)
        if updated_settings and updated_settings[2] == "gemma-3-12b-it":
            print("✅ تم تحديث الإعدادات")
        else:
            print("❌ فشل في تحديث الإعدادات")
            return False
        
        # اختبار الحذف
        delete_ai_settings(test_button_id)
        deleted_settings = get_ai_settings(test_button_id)
        
        if not deleted_settings:
            print("✅ تم حذف الإعدادات")
        else:
            print("❌ فشل في حذف الإعدادات")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار إدارة الإعدادات: {e}")
        return False

def test_api_key_selection():
    """اختبار اختيار مفاتيح API"""
    print("\n🔑 اختبار اختيار مفاتيح API...")
    
    try:
        from keyboards.builder import select_api_key
        
        # اختبار مع مفاتيح مخصصة
        test_keys = ["key1", "key2", "key3"]
        
        for i in range(5):
            key, index = select_api_key(json.dumps(test_keys), i)
            expected_index = i % len(test_keys)
            if index == expected_index:
                print(f"✅ المفتاح {index}: {key}")
            else:
                print(f"❌ خطأ في اختيار المفتاح")
                return False
        
        # اختبار مع قائمة فارغة
        key, index = select_api_key(json.dumps([]), 0)
        if key is None:
            print("✅ معالجة صحيحة للقائمة الفارغة")
        else:
            print("❌ خطأ في معالجة القائمة الفارغة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار اختيار المفاتيح: {e}")
        return False

def test_message_rendering():
    """اختبار معالجة الرسائل الديناميكية"""
    print("\n💬 اختبار معالجة الرسائل...")
    
    try:
        # إنشاء مستخدم اختبار
        from keyboards.builder import add_user
        test_user_id = 123456789
        add_user(test_user_id, "test_user", "Test", "User")
        
        # اختبار رسالة مع هاشتاجات
        test_message = "مرحباً #name! معرفك هو #id"
        rendered = render_ai_message(test_message, test_user_id)
        
        if "#name" not in rendered and "#id" not in rendered:
            print("✅ تم معالجة الهاشتاجات بنجاح")
            print(f"   الرسالة: {rendered}")
        else:
            print("❌ فشل في معالجة الهاشتاجات")
            return False
        
        # اختبار رسالة بدون هاشتاجات
        simple_message = "هذه رسالة بسيطة"
        rendered_simple = render_ai_message(simple_message, test_user_id)
        
        if rendered_simple == simple_message:
            print("✅ معالجة صحيحة للرسائل البسيطة")
        else:
            print("❌ خطأ في معالجة الرسائل البسيطة")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار معالجة الرسائل: {e}")
        return False

def test_environment_setup():
    """اختبار إعداد البيئة"""
    print("\n🌍 اختبار إعداد البيئة...")
    
    try:
        # التحقق من متغيرات البيئة
        bot_token = os.getenv("BOT_TOKEN")
        admin_id = os.getenv("ADMIN_ID")
        gemini_keys = os.getenv("GEMINI_API_KEYS")
        
        if bot_token:
            print("✅ BOT_TOKEN موجود")
        else:
            print("❌ BOT_TOKEN غير موجود")
        
        if admin_id:
            print("✅ ADMIN_ID موجود")
        else:
            print("❌ ADMIN_ID غير موجود")
        
        if gemini_keys:
            print("✅ GEMINI_API_KEYS موجود")
            if gemini_keys != "YOUR_API_KEY_1,YOUR_API_KEY_2,YOUR_API_KEY_3":
                print("✅ مفاتيح API محدثة")
            else:
                print("⚠️ مفاتيح API لم يتم تحديثها (استخدام القيم الافتراضية)")
        else:
            print("❌ GEMINI_API_KEYS غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البيئة: {e}")
        return False

def run_all_tests():
    """تشغيل جميع الاختبارات"""
    print("🧪 بدء اختبار نظام الذكاء الاصطناعي المخصص\n")
    
    tests = [
        ("إعداد البيئة", test_environment_setup),
        ("إعداد قاعدة البيانات", test_database_setup),
        ("إدارة الإعدادات", test_ai_settings_crud),
        ("اختيار مفاتيح API", test_api_key_selection),
        ("معالجة الرسائل", test_message_rendering),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"اختبار: {test_name}")
        print('='*50)
        
        if test_func():
            print(f"✅ نجح اختبار {test_name}")
            passed += 1
        else:
            print(f"❌ فشل اختبار {test_name}")
    
    print(f"\n{'='*50}")
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    print('='*50)
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء.")
    
    return passed == total

if __name__ == "__main__":
    # تحميل متغيرات البيئة
    from dotenv import load_dotenv
    load_dotenv()
    
    # تشغيل الاختبارات
    success = run_all_tests()
    
    if success:
        print("\n🚀 يمكنك الآن استخدام نظام الذكاء الاصطناعي في البوت!")
    else:
        print("\n🔧 يرجى إصلاح المشاكل قبل الاستخدام.")
