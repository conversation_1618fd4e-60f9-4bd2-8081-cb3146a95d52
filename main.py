from dotenv import load_dotenv
import os
from telebot import TeleBot
from handlers.logic import setup_handlers
from keyboards.builder import set_bot_username, init_database

load_dotenv()

BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_ID = int(os.getenv("ADMIN_ID"))
bot = TeleBot(BOT_TOKEN)

# تهيئة قاعدة البيانات
print("🔧 تهيئة قاعدة البيانات...")
init_database()
print("✅ تم تهيئة قاعدة البيانات بنجاح")

# حفظ اسم البوت للاستخدام في روابط الدعوة
try:
    bot_info = bot.get_me()
    set_bot_username(bot_info.username)
    print(f"✅ تم حفظ اسم البوت: @{bot_info.username}")
except Exception as e:
    print(f"⚠️ تعذر جلب اسم البوت: {e}")

# إعداد المعالجات
setup_handlers(bot, ADMIN_ID)

print("🤖 البوت يعمل الآن...")
bot.polling()
