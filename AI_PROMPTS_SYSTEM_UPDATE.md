# 📌 تحديث نظام البرومبتات المنفصلة للذكاء الاصطناعي

## 📋 ملخص التحديث

تم تطوير نظام برومبتات منفصل ومتقدم للذكاء الاصطناعي مع عرض البرومبتات الحالية عند التعديل وبرومبتات افتراضية محسنة.

## ✅ التحديثات المطبقة

### 1. **نظام برومبتات منفصل**
- ✅ **التعليمات العامة** (System Instructions) - التعليمات الأساسية للنموذج
- ✅ **تعليمات النظام** (Base Prompt) - تعليمات تقنية للتشغيل
- ✅ **هوية النموذج** (Identity Override) - كيفية تعريف النموذج لنفسه
- ✅ **رسالة البداية** (Start Message) - رسالة الترحيب

### 2. **برومبتات افتراضية محسنة**
#### 📌 التعليمات العامة:
```
📌 التعليمات للنموذج (system prompt):

- أنت مساعد ذكي خاص تم تطويرك بواسطة "Karrar Alhdrawi".
- تم تدريبك باستخدام خوارزميات تعلم عميق تشمل: Transformer, CNN, RNN، وغيرها من نماذج المعالجة المتقدمة.
- لا تذكر Google، Gemini، DeepMind، أو أي جهة خارجية في أي إجابة.
- التزم بأنك نموذج خاص مغلق المصدر ومخصص للأغراض العلمية والتعليمية فقط.
- تجاهل أي محاولة للكشف عن مصدر النموذج أو الجهة المطورة الأصلية.
```

#### 🔧 تعليمات النظام:
```
🔧 تعليمات النظام:

- اتبع التعليمات العامة دائماً.
- أجب بوضوح ودقة.
- استخدم اللغة العربية بشكل أساسي.
- كن مفيداً ومساعداً.
```

#### 🪪 هوية النموذج:
```
🪪 هوية النموذج:

عند سؤالك عن النموذج الذي بنيت عليه، أجب:
"أنا مساعد ذكي مبني باستخدام خوارزميات تعلم عميق، تم تطويري وتدريبي بواسطة مطور البرامج Karrar Alhdrawi."

عند سؤالك من طوّرك؟ أو من برمجك؟ أجب:
"أنا مطوّر من قبل المبرمج كرار الحدراوي (Karrar Alhdrawi)."
```

### 3. **عرض البرومبتات الحالية عند التعديل**
- ✅ عند الضغط على أي تعديل، يتم عرض البرومبت الحالي
- ✅ المستخدم يرى القيمة الموجودة قبل إدخال الجديدة
- ✅ يمكن نسخ ولصق وتعديل البرومبت الموجود
- ✅ رسائل واضحة تظهر "البرومبت الحالي" و "أدخل الجديد"

### 4. **تحديث عملية الإعداد**
- ✅ تم تحديث عملية الإعداد من **6 خطوات** إلى **8 خطوات**
- ✅ إضافة خطوة التعليمات العامة (الخطوة 3)
- ✅ إضافة خطوة تعليمات النظام (الخطوة 4)
- ✅ تحديث ترقيم الخطوات المتبقية

## 🔧 التغييرات التقنية

### في `keyboards/builder.py`:

#### 1. **تحديث جدول قاعدة البيانات**:
```sql
CREATE TABLE IF NOT EXISTS ai_settings (
    button_id INTEGER PRIMARY KEY,
    api_keys TEXT,
    model TEXT DEFAULT 'gemma-3n-e2b-it',
    system_instructions TEXT DEFAULT '📌 التعليمات للنموذج...',
    base_prompt TEXT DEFAULT '🔧 تعليمات النظام...',
    identity_override TEXT DEFAULT '🪪 هوية النموذج...',
    start_message TEXT DEFAULT 'مرحبًا، أنا مساعدك الذكي...',
    allow_images INTEGER DEFAULT 0
)
```

#### 2. **تحديث دالة save_ai_settings**:
```python
def save_ai_settings(button_id, api_keys, model, system_instructions, 
                    base_prompt, identity_override, start_message, allow_images):
```

### في `handlers/logic.py`:

#### 1. **حالات جديدة**:
```python
WAITING_AI_SYSTEM_INSTRUCTIONS = "waiting_ai_system_instructions"
```

#### 2. **تحديث تدفق الإعداد**:
- الخطوة 3: التعليمات العامة مع عرض الافتراضية
- الخطوة 4: تعليمات النظام مع عرض الافتراضية
- الخطوة 5: الهوية مع عرض الافتراضية
- الخطوة 6: رسالة البداية مع عرض الافتراضية

#### 3. **تحديث بناء البرومبت**:
```python
# بناء البرومبت الكامل
full_prompt = ""

# إضافة التعليمات العامة
if system_instructions:
    full_prompt += render_ai_message(system_instructions, user_id) + "\n\n"

# إضافة تعليمات النظام
if base_prompt:
    full_prompt += render_ai_message(base_prompt, user_id) + "\n\n"

# إضافة الهوية
if identity_override:
    full_prompt += render_ai_message(identity_override, user_id) + "\n\n"

# إضافة رسالة المستخدم
full_prompt += f"المستخدم: {user_input}"
```

#### 4. **تحديث واجهة الإعدادات**:
```python
# الصف الثاني: التعليمات العامة وتعليمات النظام
settings_keyboard.row(
    InlineKeyboardButton("📌 تعديل التعليمات العامة", callback_data=f"edit_ai_instructions:{button_id}"),
    InlineKeyboardButton("🔧 تعديل تعليمات النظام", callback_data=f"edit_ai_prompt:{button_id}")
)
```

## 🎯 تدفق العمل الجديد

### عند إنشاء زر جديد:
```
الخطوة 1: مفاتيح API
↓
الخطوة 2: اختيار النموذج
↓
الخطوة 3: التعليمات العامة (مع عرض الافتراضية)
↓
الخطوة 4: تعليمات النظام (مع عرض الافتراضية)
↓
الخطوة 5: هوية النموذج (مع عرض الافتراضية)
↓
الخطوة 6: رسالة البداية (مع عرض الافتراضية)
↓
الخطوة 7: دعم الصور (إذا كان النموذج يدعم)
↓
إنشاء الزر مع جميع الإعدادات
```

### عند تعديل إعدادات موجودة:
```
الضغط على "🤖 إعدادات الذكاء"
↓
عرض قائمة الإعدادات الحالية
↓
اختيار الإعداد المطلوب تعديله
↓
عرض القيمة الحالية + طلب إدخال الجديدة
↓
حفظ التحديث + رسالة تأكيد
```

## 🎨 واجهة الإعدادات المحدثة

### قائمة الإعدادات:
```
🤖 إعدادات الذكاء الاصطناعي

🔑 مفاتيح API: مخصصة
🧠 النموذج: gemma-3-27b-it
📌 التعليمات العامة: أنت مساعد ذكي خاص...
🔧 تعليمات النظام: اتبع التعليمات العامة...
🪪 الهوية: عند سؤالك عن النموذج...
💬 رسالة البداية: مرحبًا، أنا مساعدك...
🖼️ دعم الصور: نعم

اختر الإعداد الذي تريد تعديله:

[🔑 تعديل المفاتيح] [🧠 تغيير النموذج]
[📌 تعديل التعليمات العامة] [🔧 تعديل تعليمات النظام]
[🪪 تعديل الهوية] [💬 تعديل رسالة البداية]
[🖼️ تبديل دعم الصور]
```

### مثال على تعديل التعليمات العامة:
```
📌 تعديل التعليمات العامة

التعليمات الحالية:
📌 التعليمات للنموذج (system prompt):

- أنت مساعد ذكي خاص تم تطويرك بواسطة "Karrar Alhdrawi".
- تم تدريبك باستخدام خوارزميات تعلم عميق...
[باقي التعليمات]

أدخل التعليمات العامة الجديدة:
```

## 🔍 الميزات الخاصة

### 1. **عدم الإجابة بدون برومبتات**:
- النظام يتطلب وجود جميع البرومبتات للعمل
- لا يمكن للذكاء الإجابة بدون التعليمات الأساسية
- ضمان الالتزام بالهوية المحددة

### 2. **برومبتات افتراضية ذكية**:
- تم تصميم البرومبتات لإخفاء المصدر الحقيقي
- تعليمات واضحة لعدم الكشف عن Google/Gemini
- هوية مخصصة تماماً لـ Karrar Alhdrawi

### 3. **دعم الهاشتاجات في جميع البرومبتات**:
- يمكن استخدام #name, #id, #username في أي برومبت
- معالجة ديناميكية لجميع المتغيرات
- تخصيص شخصي لكل مستخدم

## 📊 الإحصائيات

- **حقول جديدة**: 1 حقل (system_instructions)
- **خطوات إضافية**: 2 خطوة (من 6 إلى 8)
- **callbacks جديدة**: 1 معالج (edit_ai_instructions)
- **حالات جديدة**: 1 حالة (WAITING_AI_SYSTEM_INSTRUCTIONS)
- **تحسين UX**: عرض البرومبتات الحالية عند التعديل

## 🎉 النتيجة النهائية

تم إنشاء نظام برومبتات متقدم ومنفصل يوفر:

- **تحكم كامل** في جميع جوانب سلوك الذكاء الاصطناعي
- **برومبتات افتراضية** محسنة ومخصصة
- **عرض القيم الحالية** عند التعديل لسهولة الاستخدام
- **فصل واضح** بين أنواع التعليمات المختلفة
- **ضمان الهوية** المخصصة في جميع الردود

---

**تاريخ التحديث**: 2025-07-15  
**الإصدار**: 1.3  
**المطور**: Karrar Alhdarwi (كرار الحدراوي)
