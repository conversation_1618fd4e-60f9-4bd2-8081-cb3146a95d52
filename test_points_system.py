#!/usr/bin/env python3
"""
مثال لاختبار نظام النقاط والهاشتاجات
"""

import sqlite3
from keyboards.builder import update_user_points, get_user_data, render_dynamic_message

def test_points_system():
    """اختبار نظام النقاط"""
    
    # معرف مستخدم تجريبي (استبدله بمعرفك الحقيقي)
    test_user_id = 123456789
    
    print("🧪 اختبار نظام النقاط والهاشتاجات")
    print("=" * 50)
    
    # 1. تحديث نقاط المستخدم
    print("1️⃣ تحديث النقاط...")
    update_user_points(test_user_id, 150)
    print(f"✅ تم تحديث نقاط المستخدم {test_user_id} إلى 150")
    
    # 2. جلب بيانات المستخدم
    print("\n2️⃣ جلب بيانات المستخدم...")
    user_data = get_user_data(test_user_id)
    if user_data:
        print(f"✅ بيانات المستخدم: {user_data}")
    else:
        print("❌ لم يتم العثور على المستخدم")
        return
    
    # 3. اختبار الهاشتاجات
    print("\n3️⃣ اختبار الهاشتاجات...")
    
    test_messages = [
        "مرحباً #name! معرفك هو #id",
        "لديك #points نقطة، اسم المستخدم: #username",
        "رابط الدعوة: #invitelink",
        "مرحباً #name! لديك #points نقطة. شارك رابطك: #invitelink"
    ]
    
    for i, template in enumerate(test_messages, 1):
        print(f"\n📝 مثال {i}:")
        print(f"القالب: {template}")
        result = render_dynamic_message(template, test_user_id)
        print(f"النتيجة: {result}")
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار بنجاح!")

def add_test_user():
    """إضافة مستخدم تجريبي لقاعدة البيانات"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    
    test_user_id = 123456789
    
    try:
        cursor.execute('''
            INSERT OR REPLACE INTO users (user_id, username, first_name, last_name, points)
            VALUES (?, ?, ?, ?, ?)
        ''', (test_user_id, 'testuser', 'أحمد', 'محمد', 0))
        
        conn.commit()
        print(f"✅ تم إضافة المستخدم التجريبي: {test_user_id}")
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المستخدم: {e}")
    
    finally:
        conn.close()

def show_database_info():
    """عرض معلومات قاعدة البيانات"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    
    print("\n📊 معلومات قاعدة البيانات:")
    print("-" * 30)
    
    # عدد المستخدمين
    cursor.execute('SELECT COUNT(*) FROM users')
    users_count = cursor.fetchone()[0]
    print(f"👥 عدد المستخدمين: {users_count}")
    
    # عدد الأزرار
    cursor.execute('SELECT COUNT(*) FROM buttons')
    buttons_count = cursor.fetchone()[0]
    print(f"🔘 عدد الأزرار: {buttons_count}")
    
    # عدد روابط الدعوة
    cursor.execute('SELECT COUNT(*) FROM invite_links')
    invites_count = cursor.fetchone()[0]
    print(f"🔗 عدد روابط الدعوة: {invites_count}")
    
    # آخر 5 مستخدمين
    cursor.execute('SELECT user_id, username, first_name, points FROM users ORDER BY joined_at DESC LIMIT 5')
    recent_users = cursor.fetchall()
    
    if recent_users:
        print(f"\n👤 آخر {len(recent_users)} مستخدمين:")
        for user in recent_users:
            user_id, username, first_name, points = user
            username_str = f"@{username}" if username else "بدون معرف"
            print(f"  - {first_name} ({username_str}) - {points} نقطة - ID: {user_id}")
    
    conn.close()

if __name__ == "__main__":
    print("🚀 بدء اختبار نظام الهاشتاجات الديناميكية")
    
    # عرض معلومات قاعدة البيانات
    show_database_info()
    
    # إضافة مستخدم تجريبي
    add_test_user()
    
    # اختبار النظام
    test_points_system()
