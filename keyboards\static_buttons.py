from telebot.types import ReplyKeyboardMarkup, KeyboardButton

def options_keyboard(is_admin=False):
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("قائمة"), KeyboardButton("محتوى"))
    markup.row(KeyboardButton("محتوى عشوائي"), KeyboardButton("ترجمة"))
    markup.row(KeyboardButton("ذكاء صناعي"), KeyboardButton("محتوى مخصص"))
    markup.row(KeyboardButton("MCQ"), KeyboardButton("💰 شراء النقاط"))

    # زر لوحة تحكم الأدمن يظهر فقط للأدمنيين
    if is_admin:
        markup.row(KeyboardButton("لوحة تحكم الأدمن"))

    markup.row(KeyboardButton("إلغاء"))
    return markup

def admin_control_keyboard(is_main_admin=False):
    """كيبورد لوحة تحكم الأدمن"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)

    # أزرار متاحة لجميع الأدمنيين
    markup.row(KeyboardButton("📢 إرسال إذاعة"), KeyboardButton("🎯 إدارة النقاط"))
    markup.row(KeyboardButton("📊 إحصائيات المستخدمين"))

    # أزرار مخصصة للأدمن الرئيسي فقط
    if is_main_admin:
        markup.row(KeyboardButton("➕ تعيين أدمن جديد"), KeyboardButton("🗑️ إدارة الأدمنيين"))
        markup.row(KeyboardButton("📡 إرسال إذاعة إلى القنوات"))
        markup.row(KeyboardButton("📎 تخصيص قناة للمحتوى"))
        markup.row(KeyboardButton("⚙️ إعدادات النقاط"))

    markup.row(KeyboardButton("🔙 رجوع"))
    return markup

def custom_form_field_types_keyboard():
    """كيبورد أنواع حقول النماذج المخصصة"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("نص عادي"), KeyboardButton("نص مطول"))
    markup.row(KeyboardButton("إدخال رقم"), KeyboardButton("اختيار واحد"))
    markup.row(KeyboardButton("اختيارات متعددة"))
    markup.row(KeyboardButton("إلغاء"))
    return markup

def custom_form_admin_keyboard():
    """كيبورد إدارة النماذج المخصصة للأدمن"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("➕ إضافة حقل جديد"), KeyboardButton("📋 عرض النموذج الحالي"))
    markup.row(KeyboardButton("👁️ إظهار/إخفاء النموذج"), KeyboardButton("📡 تخصيص قناة"))
    markup.row(KeyboardButton("📝 تخصيص رسائل الموافقة/الرفض"))
    markup.row(KeyboardButton("🤖 إعدادات الذكاء الاصطناعي"))
    markup.row(KeyboardButton("📊 عرض الطلبات والإحصائيات"), KeyboardButton("✅ مراجعة الطلبات"))
    markup.row(KeyboardButton("🗑️ حذف النموذج"), KeyboardButton("🔙 رجوع"))
    return markup

def form_field_management_keyboard():
    """كيبورد إدارة حقل النموذج"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("✏️ تعديل اسم الحقل"), KeyboardButton("🔄 تغيير نوع الحقل"))
    markup.row(KeyboardButton("⚠️ جعل الحقل مطلوب"), KeyboardButton("📝 تعديل الخيارات"))
    markup.row(KeyboardButton("🗑️ حذف الحقل"), KeyboardButton("🔙 رجوع"))
    return markup

def ai_settings_keyboard():
    """كيبورد إعدادات الذكاء الاصطناعي"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("🔍 تفعيل تقييم الأفكار"), KeyboardButton("🤖 تفعيل الرد التلقائي"))
    markup.row(KeyboardButton("⚙️ إعدادات التقييم"), KeyboardButton("💬 إعدادات الرد التلقائي"))
    markup.row(KeyboardButton("📝 رسائل الموافقة/الرفض"), KeyboardButton("🔙 رجوع"))
    return markup

def submission_review_keyboard():
    """كيبورد مراجعة الطلبات"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("⏳ الطلبات المعلقة"), KeyboardButton("✅ الطلبات المقبولة"))
    markup.row(KeyboardButton("❌ الطلبات المرفوضة"), KeyboardButton("📊 إحصائيات شاملة"))
    markup.row(KeyboardButton("🔙 رجوع"))
    return markup

def admin_management_keyboard():
    """كيبورد إدارة الأدمنيين"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("👥 عرض الأدمنيين"), KeyboardButton("🗑️ حذف أدمن"))
    markup.row(KeyboardButton("🔙 رجوع للوحة التحكم"))
    return markup

def points_settings_keyboard():
    """كيبورد إعدادات النقاط"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("🎯 النقاط الافتراضية"), KeyboardButton("💬 تكلفة الرسائل"))
    markup.row(KeyboardButton("🌐 تكلفة الترجمة"), KeyboardButton("🤖 تكلفة الذكاء الاصطناعي"))
    markup.row(KeyboardButton("🎁 إرسال نقاط للجميع"), KeyboardButton("📝 رسالة نفاد النقاط"))
    markup.row(KeyboardButton("📢 قناة طلبات الشراء"))
    markup.row(KeyboardButton("🔙 رجوع للوحة التحكم"))
    return markup

def purchase_confirmation_keyboard():
    """كيبورد تأكيد شراء النقاط"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("✅ نعم"), KeyboardButton("❌ لا"))
    return markup

def points_packages_keyboard():
    """كيبورد باقات النقاط"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("💎 100 نقطة"), KeyboardButton("💎 500 نقطة"))
    markup.row(KeyboardButton("💎 1000 نقطة"), KeyboardButton("💎 2000 نقطة"))
    markup.row(KeyboardButton("💎 5000 نقطة"), KeyboardButton("💎 10000 نقطة"))
    markup.row(KeyboardButton("🔙 رجوع"))
    return markup

def payment_methods_keyboard():
    """كيبورد طرق الدفع"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("💳 زين كاش"), KeyboardButton("💳 آسيا حوالة"))
    markup.row(KeyboardButton("💳 فيزا/ماستركارد"), KeyboardButton("💳 PayPal"))
    markup.row(KeyboardButton("🔙 رجوع"))
    return markup

def payment_confirmation_keyboard():
    """كيبورد تأكيد الدفع"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("✅ لقد دفعت"), KeyboardButton("❌ إلغاء العملية"))
    return markup

def cancel_operation_keyboard():
    """كيبورد إلغاء العملية"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)
    markup.row(KeyboardButton("❌ إلغاء العملية"))
    return markup

def custom_packages_keyboard(packages):
    """كيبورد باقات النقاط المخصصة"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)

    # إضافة الباقات المخصصة
    for i in range(0, len(packages), 2):  # إضافة زرين في كل صف
        row_buttons = []
        for j in range(2):  # حد أقصى زرين في الصف
            if i + j < len(packages):
                package = packages[i + j]
                if isinstance(package, (list, tuple)) and len(package) >= 3:
                    name = package[0]
                    points = package[1]
                    price = package[2]
                    button_text = f"💎 {name}"
                    row_buttons.append(KeyboardButton(button_text))

        if row_buttons:
            markup.row(*row_buttons)

    # زر الرجوع
    markup.row(KeyboardButton("🔙 رجوع"))
    return markup

def custom_payment_methods_keyboard(payment_methods):
    """كيبورد طرق الدفع المخصصة"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)

    # إضافة طرق الدفع المخصصة
    for i in range(0, len(payment_methods), 2):  # إضافة زرين في كل صف
        row_buttons = []
        for j in range(2):  # حد أقصى زرين في الصف
            if i + j < len(payment_methods):
                method = payment_methods[i + j]
                if isinstance(method, (list, tuple)) and len(method) >= 2:
                    method_name = method[0]
                    button_text = f"💳 {method_name}"
                    row_buttons.append(KeyboardButton(button_text))

        if row_buttons:
            markup.row(*row_buttons)

    # زر الرجوع
    markup.row(KeyboardButton("🔙 رجوع"))
    return markup
