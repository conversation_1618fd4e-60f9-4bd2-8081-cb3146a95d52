# دليل اختبار النماذج المخصصة التسلسلية الجديدة

## 🧪 خطوات الاختبار

### 1. إنشاء نموذج مخصص جديد
1. اب<PERSON><PERSON> البوت كأدمن
2. اضغط على "END | +"
3. أدخل اسم الزر: "طلب فكرة جديدة"
4. اختر نوع الزر: "محتوى مخصص"
5. يجب أن تظهر رسالة تأكيد إنشاء النموذج

### 2. إضافة حقول للنموذج (بالترتيب المطلوب)
1. اضغط على زر "طلب فكرة جديدة" (سيظهر لوحة إدارة للأدمن)
2. اضغط على "➕ إضافة حقل جديد"
3. أضف الحقول بالترتيب التالي:

   **الحقل الأول**: "اسم الفكرة"
   - النوع: "نص عادي"
   - مطلوب: نعم

   **الحقل الثاني**: "وصف الفكرة"
   - النوع: "نص مطول"
   - مطلوب: نعم

   **الحقل الثالث**: "هل الفكرة جديدة؟"
   - النوع: "اختيار واحد"
   - مطلوب: نعم
   - الخيارات:
     * نعم، فكرة جديدة تماماً
     * لا، تطوير لفكرة موجودة
     * غير متأكد

   **الحقل الرابع**: "المجالات المطلوبة"
   - النوع: "اختيارات متعددة"
   - مطلوب: لا (اختياري)
   - الخيارات:
     * تطوير البرمجيات
     * التصميم
     * التسويق
     * إدارة المشاريع

   **الحقل الخامس**: "الميزانية المتوقعة"
   - النوع: "إدخال رقم"
   - مطلوب: لا (اختياري)

### 3. إعداد النموذج والذكاء الاصطناعي
1. اضغط على "👁️ إظهار/إخفاء النموذج" لجعله مرئياً للمستخدمين
2. اضغط على "📡 تخصيص قناة" وأدخل معرف قناة اختبار (اختياري)
3. اضغط على "📝 تخصيص رسائل الموافقة/الرفض" وخصص الرسائل
4. اضغط على "🤖 إعدادات الذكاء الاصطناعي" وفعل التقييم التلقائي

### 4. اختبار النموذج التسلسلي كمستخدم عادي
1. ابدأ البوت كمستخدم عادي
2. يجب أن يظهر زر "طلب فكرة جديدة" في القائمة الرئيسية
3. اضغط على الزر وسيبدأ النموذج التسلسلي:

   **الحقل الأول**: "اسم الفكرة"
   - سيظهر: "📋 الحقل 1 من 5"
   - أدخل اسم الفكرة (مطلوب)
   - أزرار متاحة: [❌ إلغاء النموذج]
   - بعد الإدخال ينتقل تلقائياً للحقل التالي

   **الحقل الثاني**: "وصف الفكرة"
   - سيظهر: "📋 الحقل 2 من 5"
   - أدخل وصف مفصل (مطلوب)
   - أزرار متاحة: [⬅️ السابق] [❌ إلغاء النموذج]
   - بعد الإدخال ينتقل تلقائياً للحقل التالي

   **الحقل الثالث**: "هل الفكرة جديدة؟"
   - سيظهر: "📋 الحقل 3 من 5"
   - اختر من الخيارات المتاحة أو "✏️ أخرى"
   - أزرار متاحة: [⬅️ السابق] [❌ إلغاء النموذج]
   - بعد الاختيار ينتقل تلقائياً للحقل التالي

   **الحقل الرابع**: "المجالات المطلوبة" (اختياري)
   - سيظهر: "📋 الحقل 4 من 5"
   - اختر مجال أو أكثر، أو اضغط "✅ تم الاختيار"
   - أزرار متاحة: [⬅️ السابق] [⏭️ تخطي] [❌ إلغاء النموذج]
   - بعد الاختيار أو التخطي ينتقل للحقل التالي

   **الحقل الخامس**: "الميزانية المتوقعة" (اختياري)
   - سيظهر: "📋 الحقل 5 من 5"
   - أدخل رقم أو تخطي
   - أزرار متاحة: [⬅️ السابق] [⏭️ تخطي] [❌ إلغاء النموذج]
   - بعد الإدخال أو التخطي ينتقل للملخص

### 5. مراجعة وإرسال النموذج
1. بعد إكمال جميع الحقول، يظهر ملخص الإجابات
2. أزرار متاحة:
   - [📤 إرسال الطلب]
   - [✏️ تعديل الإجابات] (يعود للحقل الأول)
   - [❌ إلغاء]
3. اضغط على "📤 إرسال الطلب"
4. يجب أن تظهر رسالة الإكمال المخصصة
5. تحقق من وصول الطلب للأدمن مع أزرار الموافقة/الرفض

## 🔧 اختبارات إضافية

### اختبار خيار "أخرى"
- في حقول الاختيار، جرب الضغط على "✏️ أخرى" واكتب خيار مخصص

### اختبار الاختيارات المتعددة
- في حقل الاختيارات المتعددة، اختر عدة خيارات
- جرب إلغاء اختيار بعض الخيارات
- اضغط على "✅ تم" عند الانتهاء

### اختبار إدارة النموذج
- اضغط على "📊 عرض الاستجابات" لرؤية الإحصائيات
- جرب تعديل رسالة الإكمال
- جرب تغيير القناة المخصصة

### اختبار الحذف
- اضغط على "🗑️ حذف النموذج" واتبع التأكيد
- تأكد من حذف النموذج وجميع بياناته

## ✅ النتائج المتوقعة

### للأدمن:
- إمكانية إنشاء وإدارة النماذج المخصصة
- رؤية جميع النماذج (مرئية ومخفية)
- استقبال الطلبات المرسلة
- إدارة إعدادات النماذج

### للمستخدم العادي:
- رؤية النماذج المرئية فقط
- ملء النماذج بسهولة
- استخدام خيارات الاختيار والخيارات المخصصة
- تلقي رسالة تأكيد بعد الإرسال

## 🐛 مشاكل محتملة

### إذا لم يظهر النموذج للمستخدمين:
- تأكد من أن النموذج مرئي (👁️ إظهار/إخفاء النموذج)
- تأكد من إعادة تشغيل البوت بعد إنشاء النموذج

### إذا لم تصل الطلبات:
- تأكد من صحة معرف القناة
- تأكد من أن البوت له صلاحيات في القناة
- إذا لم تحدد قناة، ستصل الطلبات للأدمن مباشرة

### إذا ظهرت أخطاء:
- تحقق من سجلات البوت
- تأكد من تحديث قاعدة البيانات
- أعد تشغيل البوت

## 📝 ملاحظات

- النماذج المخصصة تدعم جميع أنواع الحقول المطلوبة
- يمكن إضافة عدد غير محدود من الحقول
- الاستجابات محفوظة في قاعدة البيانات
- النظام يدعم الهاشتاجات الديناميكية في رسائل الإكمال
- يمكن حذف النماذج وجميع بياناتها
