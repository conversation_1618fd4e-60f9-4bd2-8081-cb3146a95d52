# دليل اختبار النماذج المخصصة

## 🧪 خطوات الاختبار

### 1. إنشاء نموذج مخصص جديد
1. ابد<PERSON> البوت كأدمن
2. اضغط على "END | +" 
3. أدخل اسم الزر: "طلب خدمة"
4. اختر نوع الزر: "محتوى مخصص"
5. يجب أن تظهر رسالة تأكيد إنشاء النموذج

### 2. إضافة حقول للنموذج
1. اضغط على زر "طلب خدمة" (سيظهر لوحة إدارة للأدمن)
2. اضغط على "➕ إضافة حقل جديد"
3. اختر "نص عادي"
4. أدخل اسم الحقل: "الاسم الكامل"
5. كرر العملية لإضافة:
   - حقل "نص مطول" باسم "وصف الطلب"
   - حقل "إدخال رقم" باسم "رقم الهاتف"
   - حقل "اختيار واحد" باسم "نوع الخدمة" مع خيارات:
     * تصميم موقع
     * تطوير تطبيق
     * استشارة تقنية
   - حقل "اختيارات متعددة" باسم "المهارات المطلوبة" مع خيارات:
     * HTML/CSS
     * JavaScript
     * Python
     * تصميم UI/UX

### 3. إعداد النموذج
1. اضغط على "👁️ إظهار/إخفاء النموذج" لجعله مرئياً للمستخدمين
2. اضغط على "📡 تخصيص قناة" وأدخل معرف قناة اختبار (اختياري)
3. اضغط على "📝 تخصيص رسالة الإكمال" وأدخل رسالة مخصصة

### 4. اختبار النموذج كمستخدم عادي
1. ابدأ البوت كمستخدم عادي
2. يجب أن يظهر زر "طلب خدمة" في القائمة الرئيسية
3. اضغط على الزر وابدأ ملء الحقول:
   - أدخل اسمك في حقل "الاسم الكامل"
   - أدخل وصف مفصل في حقل "وصف الطلب"
   - أدخل رقم هاتف في حقل "رقم الهاتف"
   - اختر خدمة من قائمة "نوع الخدمة"
   - اختر مهارات متعددة من قائمة "المهارات المطلوبة"

### 5. إرسال النموذج
1. بعد ملء جميع الحقول، يجب أن يظهر ملخص الإجابات
2. اضغط على "📤 إرسال الطلب"
3. يجب أن تظهر رسالة الإكمال المخصصة
4. تحقق من وصول الطلب للقناة المحددة أو للأدمن

## 🔧 اختبارات إضافية

### اختبار خيار "أخرى"
- في حقول الاختيار، جرب الضغط على "✏️ أخرى" واكتب خيار مخصص

### اختبار الاختيارات المتعددة
- في حقل الاختيارات المتعددة، اختر عدة خيارات
- جرب إلغاء اختيار بعض الخيارات
- اضغط على "✅ تم" عند الانتهاء

### اختبار إدارة النموذج
- اضغط على "📊 عرض الاستجابات" لرؤية الإحصائيات
- جرب تعديل رسالة الإكمال
- جرب تغيير القناة المخصصة

### اختبار الحذف
- اضغط على "🗑️ حذف النموذج" واتبع التأكيد
- تأكد من حذف النموذج وجميع بياناته

## ✅ النتائج المتوقعة

### للأدمن:
- إمكانية إنشاء وإدارة النماذج المخصصة
- رؤية جميع النماذج (مرئية ومخفية)
- استقبال الطلبات المرسلة
- إدارة إعدادات النماذج

### للمستخدم العادي:
- رؤية النماذج المرئية فقط
- ملء النماذج بسهولة
- استخدام خيارات الاختيار والخيارات المخصصة
- تلقي رسالة تأكيد بعد الإرسال

## 🐛 مشاكل محتملة

### إذا لم يظهر النموذج للمستخدمين:
- تأكد من أن النموذج مرئي (👁️ إظهار/إخفاء النموذج)
- تأكد من إعادة تشغيل البوت بعد إنشاء النموذج

### إذا لم تصل الطلبات:
- تأكد من صحة معرف القناة
- تأكد من أن البوت له صلاحيات في القناة
- إذا لم تحدد قناة، ستصل الطلبات للأدمن مباشرة

### إذا ظهرت أخطاء:
- تحقق من سجلات البوت
- تأكد من تحديث قاعدة البيانات
- أعد تشغيل البوت

## 📝 ملاحظات

- النماذج المخصصة تدعم جميع أنواع الحقول المطلوبة
- يمكن إضافة عدد غير محدود من الحقول
- الاستجابات محفوظة في قاعدة البيانات
- النظام يدعم الهاشتاجات الديناميكية في رسائل الإكمال
- يمكن حذف النماذج وجميع بياناتها
