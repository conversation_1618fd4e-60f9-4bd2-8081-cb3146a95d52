import sqlite3
import json
import random
import os
from telebot.types import ReplyKeyboardMarkup, KeyboardButton
from deep_translator import GoogleTranslator
import google.genai as genai

# إعداد قاعدة البيانات
def init_database():
    """تهيئة قاعدة البيانات وإنشاء الجدول إذا لم يكن موجوداً"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # إنشاء الجدول الأساسي
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS buttons (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            label TEXT NOT NULL,
            type TEXT NOT NULL,
            data TEXT,
            parent_id INTEGER,
            created_by INTEGER,
            row_index INTEGER DEFAULT 0,
            position INTEGER DEFAULT 0
        )
    ''')

    # إضافة الأعمدة الجديدة إذا لم تكن موجودة (للتوافق مع قواعد البيانات الموجودة)
    try:
        cursor.execute('ALTER TABLE buttons ADD COLUMN row_index INTEGER DEFAULT 0')
    except sqlite3.OperationalError:
        pass  # العمود موجود بالفعل

    try:
        cursor.execute('ALTER TABLE buttons ADD COLUMN position INTEGER DEFAULT 0')
    except sqlite3.OperationalError:
        pass  # العمود موجود بالفعل

    # إنشاء جدول الأدمن
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS admins (
            user_id INTEGER PRIMARY KEY
        )
    ''')

    # إنشاء جدول الإعدادات
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS settings (
            key TEXT PRIMARY KEY,
            value TEXT
        )
    ''')

    # إنشاء جدول المستخدمين لتتبع جميع المستخدمين للإذاعة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            first_name TEXT,
            last_name TEXT,
            joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # إنشاء جدول أجزاء المحتوى
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS content_parts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            button_id INTEGER,
            type TEXT,
            file_id TEXT,
            caption TEXT,
            text TEXT,
            position INTEGER
        )
    ''')

    # إضافة حقل النقاط لجدول المستخدمين
    try:
        cursor.execute('ALTER TABLE users ADD COLUMN points INTEGER DEFAULT 0')
    except sqlite3.OperationalError:
        pass  # العمود موجود بالفعل

    # إنشاء جدول إعدادات النقاط
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS points_settings (
            key TEXT PRIMARY KEY,
            value TEXT NOT NULL,
            description TEXT
        )
    ''')

    # إضافة الإعدادات الافتراضية
    default_settings = [
        ('default_points', '0', 'النقاط الافتراضية للمستخدمين الجدد'),
        ('message_cost', '1', 'تكلفة الرسالة الواحدة بالنقاط'),
        ('translation_cost', '2', 'تكلفة الترجمة بالنقاط'),
        ('ai_text_cost', '3', 'تكلفة رسالة الذكاء الاصطناعي النصية'),
        ('ai_image_cost', '5', 'تكلفة رسالة الذكاء الاصطناعي مع صورة'),
        ('no_points_message', 'عذراً، لقد نفدت نقاطك! يرجى شراء المزيد من النقاط للمتابعة.', 'رسالة نفاد النقاط'),
        ('purchase_requests_channel', '', 'معرف قناة طلبات الشراء (اتركه فارغ لاستقبال الطلبات في البوت)')
    ]

    for key, value, description in default_settings:
        cursor.execute('''
            INSERT OR IGNORE INTO points_settings (key, value, description)
            VALUES (?, ?, ?)
        ''', (key, value, description))

    # إنشاء جدول طلبات شراء النقاط
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS purchase_requests (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            points_amount INTEGER NOT NULL,
            package_name TEXT NOT NULL,
            payment_method TEXT NOT NULL,
            payment_image_file_id TEXT,
            status TEXT DEFAULT 'pending',
            request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            admin_response TEXT,
            processed_time TIMESTAMP
        )
    ''')

    # إنشاء جدول إعدادات باقات النقاط
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS points_packages (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            points INTEGER NOT NULL,
            price TEXT NOT NULL,
            description TEXT
        )
    ''')

    # إنشاء جدول إعدادات النماذج المخصصة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS custom_form_settings (
            form_id INTEGER PRIMARY KEY AUTOINCREMENT,
            button_id INTEGER UNIQUE NOT NULL,
            channel_id TEXT,
            is_visible INTEGER DEFAULT 0,
            completion_message TEXT DEFAULT 'تم إرسال طلبك بنجاح! سيتم مراجعته قريباً.',
            created_by INTEGER,
            FOREIGN KEY (button_id) REFERENCES buttons (id)
        )
    ''')

    # إنشاء جدول استجابات النماذج المخصصة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_form_responses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            form_id INTEGER NOT NULL,
            field_button_id INTEGER NOT NULL,
            response_data TEXT NOT NULL,
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (form_id) REFERENCES custom_form_settings (form_id),
            FOREIGN KEY (field_button_id) REFERENCES buttons (id)
        )
    ''')

    # إنشاء جدول خيارات الحقول (للاختيارات الواحدة والمتعددة)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS form_field_options (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            field_button_id INTEGER NOT NULL,
            option_text TEXT NOT NULL,
            position INTEGER DEFAULT 0,
            FOREIGN KEY (field_button_id) REFERENCES buttons (id)
        )
    ''')

    # إضافة عمود required للأزرار (للحقول المطلوبة)
    try:
        cursor.execute('ALTER TABLE buttons ADD COLUMN required INTEGER DEFAULT 0')
    except sqlite3.OperationalError:
        pass  # العمود موجود بالفعل

    # إنشاء جدول حالات النماذج للمستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS user_form_progress (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            form_id INTEGER NOT NULL,
            current_field_index INTEGER DEFAULT 0,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id, form_id),
            FOREIGN KEY (form_id) REFERENCES custom_form_settings (form_id)
        )
    ''')

    # إنشاء جدول طلبات النماذج (للموافقة/الرفض)
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS form_submissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            form_id INTEGER NOT NULL,
            responses_data TEXT NOT NULL,
            status TEXT DEFAULT 'pending',
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            reviewed_at TIMESTAMP,
            admin_response TEXT,
            ai_evaluation TEXT,
            FOREIGN KEY (form_id) REFERENCES custom_form_settings (form_id)
        )
    ''')

    # إنشاء جدول إعدادات الذكاء الاصطناعي للنماذج
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS form_ai_settings (
            form_id INTEGER PRIMARY KEY,
            evaluation_enabled INTEGER DEFAULT 0,
            evaluation_api_keys TEXT,
            evaluation_model TEXT DEFAULT 'gemma-3-27b-it',
            evaluation_prompt TEXT,
            auto_reply_enabled INTEGER DEFAULT 0,
            auto_reply_api_keys TEXT,
            auto_reply_model TEXT DEFAULT 'gemma-3n-e2b-it',
            auto_reply_prompt TEXT,
            approval_message TEXT DEFAULT 'تم قبول طلبك! شكراً لك.',
            rejection_message TEXT DEFAULT 'نعتذر، لم يتم قبول طلبك هذه المرة.',
            FOREIGN KEY (form_id) REFERENCES custom_form_settings (form_id)
        )
    ''')

    # إضافة باقات النقاط الافتراضية
    default_packages = [
        ('💎 100 نقطة', 100, '5000 دينار', 'باقة صغيرة للاستخدام الأساسي'),
        ('💎 500 نقطة', 500, '20000 دينار', 'باقة متوسطة للاستخدام المنتظم'),
        ('💎 1000 نقطة', 1000, '35000 دينار', 'باقة كبيرة للاستخدام المكثف'),
        ('💎 2000 نقطة', 2000, '60000 دينار', 'باقة ممتازة للاستخدام الاحترافي'),
        ('💎 5000 نقطة', 5000, '120000 دينار', 'باقة فاخرة للاستخدام المتقدم'),
        ('💎 10000 نقطة', 10000, '200000 دينار', 'باقة VIP للاستخدام غير المحدود')
    ]

    for name, points, price, description in default_packages:
        cursor.execute('''
            INSERT OR IGNORE INTO points_packages (name, points, price, description)
            VALUES (?, ?, ?, ?)
        ''', (name, points, price, description))

    # إنشاء جدول إعدادات طرق الدفع
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS payment_methods (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            details TEXT NOT NULL,
            instructions TEXT,
            is_active INTEGER DEFAULT 1
        )
    ''')

    # إضافة طرق الدفع الافتراضية
    default_payment_methods = [
        ('💳 زين كاش', '07801234567', 'قم بتحويل المبلغ إلى رقم زين كاش المذكور وأرسل صورة الإيصال'),
        ('💳 آسيا حوالة', 'فرع الكرادة - اسم المستلم: أحمد محمد', 'قم بإرسال الحوالة إلى الفرع المذكور وأرسل صورة الإيصال'),
        ('💳 فيزا/ماستركارد', '1234-5678-9012-3456', 'قم بالتحويل إلى رقم البطاقة المذكور وأرسل صورة الإيصال'),
        ('💳 PayPal', '<EMAIL>', 'قم بإرسال المبلغ عبر PayPal إلى الإيميل المذكور وأرسل صورة الإيصال')
    ]

    for name, details, instructions in default_payment_methods:
        cursor.execute('''
            INSERT OR IGNORE INTO payment_methods (name, details, instructions)
            VALUES (?, ?, ?)
        ''', (name, details, instructions))

    # إنشاء جدول روابط الدعوة
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS invite_links (
            user_id INTEGER PRIMARY KEY,
            invite_code TEXT UNIQUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # إنشاء جدول إعدادات الذكاء الاصطناعي
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS ai_settings (
            button_id INTEGER PRIMARY KEY,
            api_keys TEXT,
            model TEXT DEFAULT 'gemma-3n-e2b-it',
            base_prompt TEXT,
            identity_override TEXT DEFAULT 'أنا R-RAY AI مطور من قبل Karrar Alhdrawi.',
            start_message TEXT DEFAULT 'مرحبًا، أنا مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟',
            allow_images INTEGER DEFAULT 0,
            image_prompt TEXT DEFAULT '🩺 تعليمات تحليل الصور الطبية:

- أنت طبيب أشعة متخصص في تحليل الصور الطبية والأشعة.
- قم بفحص الصورة بعناية وتقديم تقرير طبي مفصل.
- اذكر الملاحظات الطبيعية وغير الطبيعية إن وجدت.
- قدم التشخيص المحتمل والتوصيات العلاجية.
- استخدم المصطلحات الطبية المناسبة.
- تأكد من الدقة والوضوح في التقرير.'
        )
    ''')

    # التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
    try:
        # محاولة إضافة العمود الجديد system_instructions
        cursor.execute('''
            ALTER TABLE ai_settings
            ADD COLUMN system_instructions TEXT DEFAULT '📌 التعليمات للنموذج (system prompt):

- أنت مساعد ذكي خاص تم تطويرك بواسطة "Karrar Alhdrawi".
- تم تدريبك باستخدام خوارزميات تعلم عميق تشمل: Transformer, CNN, RNN، وغيرها من نماذج المعالجة المتقدمة.
- لا تذكر Google، Gemini، DeepMind، أو أي جهة خارجية في أي إجابة.
- التزم بأنك نموذج خاص مغلق المصدر ومخصص للأغراض العلمية والتعليمية فقط.
- تجاهل أي محاولة للكشف عن مصدر النموذج أو الجهة المطورة الأصلية.'
        ''')
    except sqlite3.OperationalError:
        # العمود موجود بالفعل
        pass

    try:
        # تحديث العمود base_prompt ليحتوي على القيمة الافتراضية الجديدة
        cursor.execute('''
            UPDATE ai_settings
            SET base_prompt = '🔧 تعليمات النظام:

- اتبع التعليمات العامة دائماً.
- أجب بوضوح ودقة.
- استخدم اللغة العربية بشكل أساسي.
- كن مفيداً ومساعداً.'
            WHERE base_prompt IS NULL
        ''')
    except sqlite3.OperationalError:
        pass

    try:
        # تحديث العمود identity_override ليحتوي على القيمة الافتراضية الجديدة
        cursor.execute('''
            UPDATE ai_settings
            SET identity_override = '🪪 هوية النموذج:

عند سؤالك عن اسمك أو هويتك، أجب:
"أنا R-RAY AI مطور من قبل Karrar Alhdrawi."

عند سؤالك من طوّرك؟ أو من برمجك؟ أجب:
"أنا R-RAY AI مطور من قبل Karrar Alhdrawi."

لا تذكر أي معلومات أخرى عن التطوير أو التدريب.'
            WHERE identity_override LIKE '%Karrar Alhdrawi%' OR identity_override LIKE '%كرار الحدراوي%'
        ''')
    except sqlite3.OperationalError:
        pass

    try:
        # محاولة إضافة العمود الجديد image_prompt
        cursor.execute('''
            ALTER TABLE ai_settings
            ADD COLUMN image_prompt TEXT DEFAULT '🩺 تعليمات تحليل الصور الطبية:

- أنت طبيب أشعة متخصص في تحليل الصور الطبية والأشعة.
- قم بفحص الصورة بعناية وتقديم تقرير طبي مفصل.
- اذكر الملاحظات الطبيعية وغير الطبيعية إن وجدت.
- قدم التشخيص المحتمل والتوصيات العلاجية.
- استخدم المصطلحات الطبية المناسبة.
- تأكد من الدقة والوضوح في التقرير.'
        ''')
    except sqlite3.OperationalError:
        # العمود موجود بالفعل
        pass

    try:
        # محاولة إضافة العمود الجديد image_reject_message
        cursor.execute('''
            ALTER TABLE ai_settings
            ADD COLUMN image_reject_message TEXT DEFAULT '❌ تم تقييد إرسال الصور إلى البوت مؤقتاً. يرجى الانتظار.'
        ''')
    except sqlite3.OperationalError:
        # العمود موجود بالفعل
        pass

    # إنشاء جدول إعدادات الشراء
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS purchase_settings (
            button_id INTEGER PRIMARY KEY,
            title TEXT DEFAULT 'شراء النقاط',
            description TEXT DEFAULT 'اختر الباقة المناسبة لك',
            packages TEXT DEFAULT '[]',
            payment_methods TEXT DEFAULT '[]',
            success_message TEXT DEFAULT 'تم إرسال طلبك بنجاح! سيتم مراجعته قريباً.',
            custom_channel TEXT DEFAULT '',
            FOREIGN KEY (button_id) REFERENCES buttons (id) ON DELETE CASCADE
        )
    ''')

    # إضافة العمود الجديد custom_channel إذا لم يكن موجوداً
    try:
        cursor.execute('ALTER TABLE purchase_settings ADD COLUMN custom_channel TEXT DEFAULT ""')
    except sqlite3.OperationalError:
        pass  # العمود موجود بالفعل

    conn.commit()
    conn.close()

# وظائف قاعدة البيانات
def create_button(label, button_type, data=None, parent_id=None, created_by=None, row_index=None, position=None):
    """إنشاء زر جديد في قاعدة البيانات مع تحديد الصف والموقع"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # تحديد row_index إذا لم يتم تمريره
    if row_index is None:
        if parent_id is None:
            cursor.execute('SELECT MAX(row_index) FROM buttons WHERE parent_id IS NULL')
        else:
            cursor.execute('SELECT MAX(row_index) FROM buttons WHERE parent_id = ?', (parent_id,))
        max_row = cursor.fetchone()[0]
        row_index = (max_row + 1) if max_row is not None else 0

    # تحديد position إذا لم يتم تمريره
    if position is None:
        if parent_id is None:
            cursor.execute('SELECT MAX(position) FROM buttons WHERE parent_id IS NULL AND row_index = ?', (row_index,))
        else:
            cursor.execute('SELECT MAX(position) FROM buttons WHERE parent_id = ? AND row_index = ?', (parent_id, row_index))
        max_pos = cursor.fetchone()[0]
        position = (max_pos + 1) if max_pos is not None else 0

    cursor.execute('''
        INSERT INTO buttons (label, type, data, parent_id, created_by, row_index, position)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (label, button_type, data, parent_id, created_by, row_index, position))
    button_id = cursor.lastrowid
    conn.commit()
    conn.close()
    return button_id

def create_button_in_row(label, button_type, data=None, parent_id=None, created_by=None, target_row_index=0):
    """إنشاء زر جديد في صف محدد (يضاف على يمين الأزرار الموجودة)"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # استخراج أعلى قيمة position في الصف المحدد
    if parent_id is None:
        cursor.execute('''
            SELECT MAX(position) FROM buttons
            WHERE parent_id IS NULL AND row_index = ?
        ''', (target_row_index,))
    else:
        cursor.execute('''
            SELECT MAX(position) FROM buttons
            WHERE parent_id = ? AND row_index = ?
        ''', (parent_id, target_row_index))

    max_position = cursor.fetchone()[0]
    new_position = (max_position + 1) if max_position is not None else 0

    # إضافة الزر الجديد في الموقع الجديد (على يمين الأزرار الموجودة)
    cursor.execute('''
        INSERT INTO buttons (label, type, data, parent_id, created_by, row_index, position)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (label, button_type, data, parent_id, created_by, target_row_index, new_position))

    button_id = cursor.lastrowid
    conn.commit()
    conn.close()
    return button_id

def get_buttons(parent_id=None):
    """جلب الأزرار من قاعدة البيانات مرتبة حسب الصف والموقع"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    if parent_id is None:
        cursor.execute('SELECT * FROM buttons WHERE parent_id IS NULL ORDER BY row_index, position')
    else:
        cursor.execute('SELECT * FROM buttons WHERE parent_id = ? ORDER BY row_index, position', (parent_id,))
    buttons = cursor.fetchall()
    conn.close()
    return buttons

def get_buttons_by_row(parent_id=None):
    """جلب الأزرار مجمعة حسب الصف"""
    buttons = get_buttons(parent_id)
    rows = {}

    for button in buttons:
        # التعامل مع الأعمدة الجديدة والقديمة
        if len(button) >= 8:  # يحتوي على row_index و position
            row_idx = button[6] if button[6] is not None else 0
        else:  # قاعدة بيانات قديمة - استخدام ID كمؤشر للصف
            row_idx = button[0] - 1 if button[0] > 0 else 0

        if row_idx not in rows:
            rows[row_idx] = []
        rows[row_idx].append(button)

    return rows

def get_button_by_id(button_id):
    """جلب زر محدد بواسطة ID"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM buttons WHERE id = ?', (button_id,))
    button = cursor.fetchone()
    conn.close()
    return button

def get_button_by_label(label, parent_id=None):
    """جلب زر محدد بواسطة النص"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    if parent_id is None:
        cursor.execute('SELECT * FROM buttons WHERE label = ? AND parent_id IS NULL', (label,))
    else:
        cursor.execute('SELECT * FROM buttons WHERE label = ? AND parent_id = ?', (label, parent_id))
    button = cursor.fetchone()
    conn.close()
    return button

# وظائف بناء الكيبورد
def generate_keyboard(user_id=None, admin_id=None, parent_id=None):
    """بناء الكيبورد الرئيسي أو الفرعي مع الترتيب الصحيح"""
    markup = ReplyKeyboardMarkup(resize_keyboard=True)

    # التحقق من كون المستخدم أدمن (أدمن رئيسي أو أدمن إضافي)
    is_admin_user = False
    if user_id is not None and admin_id is not None:
        if user_id == admin_id:
            is_admin_user = True
        else:
            # التحقق من كونه أدمن إضافي
            conn = sqlite3.connect('data/database.db')
            cursor = conn.cursor()
            cursor.execute('SELECT user_id FROM admins WHERE user_id = ?', (user_id,))
            is_admin_user = cursor.fetchone() is not None
            conn.close()

    # جلب الأزرار مجمعة حسب الصف
    button_rows = get_buttons_by_row(parent_id)

    # إذا كانت القائمة الرئيسية ولا توجد أزرار مخصصة
    if parent_id is None and not button_rows:
        # للأدمن: عرض زر إنشاء أزرار جديدة
        if is_admin_user:
            markup.add(KeyboardButton("END | +"))
            return markup
        # للمستخدمين العاديين: عرض رسالة أو كيبورد فارغ
        else:
            # يمكن إضافة رسالة ترحيب أو أزرار ثابتة هنا إذا لزم الأمر
            return markup

    # بناء كل صف
    for row_idx in sorted(button_rows.keys()):
        row_buttons = button_rows[row_idx]
        current_row = []

        # إضافة أزرار الصف (مرتبة حسب position) مع فلترة حسب الصلاحيات
        for button in row_buttons:
            label = button[1]  # العمود الثاني هو label
            button_type = button[2]  # العمود الثالث هو النوع

            # إخفاء أزرار "لوحة تحكم الأدمن" عن المستخدمين العاديين
            if button_type == "لوحة تحكم الأدمن" and not is_admin_user:
                continue  # تخطي هذا الزر

            # إخفاء النماذج المخصصة غير المرئية عن المستخدمين العاديين
            if button_type == "محتوى مخصص" and not is_admin_user:
                # التحقق من رؤية النموذج
                form_settings = get_custom_form_by_button_id(button[0])  # button[0] هو id
                if not form_settings or not form_settings[3]:  # is_visible
                    continue  # تخطي هذا الزر

            current_row.append(label)

        # إضافة زر التحكم للأدمن في نهاية كل صف (فقط إذا كان هناك أزرار في الصف)
        if is_admin_user and current_row:
            current_row.append(f"{row_idx} | +")

        # إضافة الصف إلى الكيبورد (فقط إذا كان هناك أزرار)
        if current_row:
            markup.row(*[KeyboardButton(text) for text in current_row])

    # إضافة زر END | + للأدمن
    if is_admin_user:
        markup.add(KeyboardButton("END | +"))

    # إضافة أزرار التنقل للقوائم الفرعية
    if parent_id is not None:
        add_navigation_buttons(markup, user_id)

    return markup

def add_navigation_buttons(markup, user_id):
    """إضافة أزرار التنقل حسب المتطلبات الصارمة"""
    # استيراد محلي لتجنب circular import
    try:
        from handlers.logic import user_navigation
    except ImportError:
        # في حالة عدم توفر user_navigation، لا نضيف أزرار تنقل متقدمة
        markup.add(KeyboardButton("🏠 الرجوع إلى الرئيسية"))
        return

    # التحقق من وجود تاريخ تنقل للمستخدم
    has_navigation_history = (user_id in user_navigation and
                             len(user_navigation[user_id]) > 0)

    # التحقق من أن القائمة السابقة ليست الرئيسية
    has_non_main_previous = (has_navigation_history and
                            user_navigation[user_id][-1] is not None)

    navigation_row = []

    # 🔙 زر "الرجوع إلى السابقة" - يظهر فقط إذا كانت القائمة السابقة ليست الرئيسية
    if has_non_main_previous:
        navigation_row.append(KeyboardButton("🔙 الرجوع إلى السابقة"))

    # 🏠 زر "الرجوع إلى الرئيسية" - يظهر دائماً في القوائم الفرعية
    navigation_row.append(KeyboardButton("🏠 الرجوع إلى الرئيسية"))

    # إضافة صف أزرار التنقل
    if navigation_row:
        markup.row(*navigation_row)

def generate_menu_keyboard(menu_button_id, user_id=None, admin_id=None):
    """بناء كيبورد القائمة الفرعية"""
    return generate_keyboard(user_id, admin_id, menu_button_id)

# وظائف معالجة أنواع الأزرار
def process_button_action(button, user_id=None):
    """معالجة الضغط على زر حسب نوعه"""
    # استخراج البيانات الأساسية فقط التي نحتاجها
    button_id = button[0]
    label = button[1]
    button_type = button[2]
    data = button[3] if len(button) > 3 else None

    if button_type == "قائمة":
        # إذا كان هناك رد مخصص للقائمة، أرسله أولاً ثم افتح القائمة
        if data and data.strip():
            return "menu_with_response", {"menu_id": button_id, "response": data, "label": label, "user_id": user_id}
        else:
            # لا يوجد رد مخصص، أرسل اسم الزر كرد افتراضي ثم افتح القائمة
            return "menu_with_response", {"menu_id": button_id, "response": label, "label": label, "user_id": user_id}

    elif button_type == "محتوى":
        # استخدام النظام الجديد لأجزاء المحتوى
        parts = get_content_parts(button_id)
        if parts:
            return "content_parts", {"parts": parts, "user_id": user_id}

        # للتوافق مع البيانات القديمة
        if data:
            try:
                # محاولة تحليل البيانات كـ JSON للمحتوى من القناة
                import json
                content_data = json.loads(data)
                if 'channel_id' in content_data:
                    if 'message_id' in content_data:
                        # رسالة واحدة
                        return "channel_content", content_data
                    elif 'message_ids' in content_data:
                        # ألبوم (رسائل متعددة)
                        return "channel_album", content_data
            except:
                pass
        return "content", {"data": data if data else "لا يوجد محتوى محدد.", "user_id": user_id}

    elif button_type == "محتوى عشوائي":
        if data:
            try:
                import json
                button_ids = json.loads(data)  # قائمة معرفات الأزرار
                if button_ids:
                    random_button_id = random.choice(button_ids)
                    parts = get_content_parts(random_button_id)
                    if parts:
                        return "content_parts", {"parts": parts, "user_id": user_id}

                # للتوافق مع البيانات القديمة
                content_data = button_ids
                if 'channel_id' in content_data and 'message_ids' in content_data:
                    # اختيار message_id عشوائي من القائمة
                    random_message_id = random.choice(content_data['message_ids'])
                    return "channel_content", {
                        'channel_id': content_data['channel_id'],
                        'message_id': random_message_id
                    }
                else:
                    # للتوافق مع البيانات القديمة (قائمة نصوص)
                    content_list = content_data if isinstance(content_data, list) else [content_data]
                    random_content = random.choice(content_list)
                    return "content", {"data": random_content, "user_id": user_id}
            except:
                return "content", {"data": "خطأ في تحميل المحتوى العشوائي.", "user_id": user_id}
        return "content", {"data": "لا يوجد محتوى عشوائي محدد.", "user_id": user_id}

    elif button_type == "ترجمة":
        return "translate", None

    elif button_type == "ذكاء صناعي":
        return "ai_chat", {"button_id": button_id, "user_id": user_id}



    elif button_type == "MCQ":
        return "content", {"data": data if data else "❓ ميزة الأسئلة متعددة الخيارات ستكون متاحة قريباً.", "user_id": user_id}

    elif button_type == "لوحة تحكم الأدمن":
        return "admin_panel", None

    elif button_type == "💰 شراء النقاط":
        return "purchase_points", {"button_id": button_id, "data": data, "user_id": user_id}

    elif button_type == "محتوى مخصص":
        return "custom_form", {"button_id": button_id, "user_id": user_id}

    elif button_type in ["نص عادي", "نص مطول", "إدخال رقم", "اختيار واحد", "اختيارات متعددة"]:
        return "custom_form_field", {"button_id": button_id, "field_type": button_type, "user_id": user_id}

    return "content", {"data": "نوع زر غير معروف.", "user_id": user_id}

def translate_text(text, target_lang='ar'):
    """ترجمة النص باستخدام deep_translator"""
    try:
        translator = GoogleTranslator(source='auto', target=target_lang)
        translated = translator.translate(text)
        return translated
    except Exception as e:
        return f"خطأ في الترجمة: {str(e)}"

def translate_text_with_points(text, user_id, target_lang='ar'):
    """ترجمة النص مع خصم النقاط"""
    # التحقق من النقاط المطلوبة
    translation_cost = int(get_points_setting('translation_cost', '2'))

    if not check_user_points(user_id, translation_cost):
        no_points_msg = get_points_setting('no_points_message', 'عذراً، لقد نفدت نقاطك!')
        return False, no_points_msg

    # خصم النقاط
    success, result = deduct_user_points(user_id, translation_cost)
    if not success:
        return False, result

    # تنفيذ الترجمة
    translated = translate_text(text, target_lang)
    return True, translated

def process_custom_template(template_data, user_inputs):
    """معالجة النموذج المخصص"""
    try:
        template_info = json.loads(template_data) if template_data else {}
        template = template_info.get('template', 'مرحباً!')

        # استبدال المتغيرات في النموذج
        for key, value in user_inputs.items():
            template = template.replace(f'{{{key}}}', str(value))

        return template
    except:
        return "خطأ في معالجة النموذج المخصص."

# وظائف إدارة الأدمن والإعدادات
def add_admin(user_id):
    """إضافة أدمن جديد"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    try:
        cursor.execute('INSERT INTO admins (user_id) VALUES (?)', (user_id,))
        conn.commit()
        return True
    except sqlite3.IntegrityError:
        return False  # الأدمن موجود بالفعل
    finally:
        conn.close()

def is_admin(user_id, main_admin_id):
    """التحقق من كون المستخدم أدمن"""
    if user_id == main_admin_id:
        return True

    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT user_id FROM admins WHERE user_id = ?', (user_id,))
    result = cursor.fetchone()
    conn.close()
    return result is not None

def get_setting(key):
    """جلب إعداد من قاعدة البيانات"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT value FROM settings WHERE key = ?', (key,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else None

def set_setting(key, value):
    """حفظ إعداد في قاعدة البيانات"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)', (key, value))
    conn.commit()
    conn.close()

def add_user(user_id, username=None, first_name=None, last_name=None):
    """إضافة مستخدم جديد لقاعدة البيانات"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    try:
        # التحقق من وجود المستخدم
        cursor.execute('SELECT user_id FROM users WHERE user_id = ?', (user_id,))
        existing_user = cursor.fetchone()

        if not existing_user:
            # مستخدم جديد - إضافة النقاط الافتراضية
            default_points = int(get_points_setting('default_points', '0'))
            cursor.execute('''
                INSERT INTO users (user_id, username, first_name, last_name, points)
                VALUES (?, ?, ?, ?, ?)
            ''', (user_id, username, first_name, last_name, default_points))
        else:
            # مستخدم موجود - تحديث البيانات فقط
            cursor.execute('''
                UPDATE users SET username = ?, first_name = ?, last_name = ?
                WHERE user_id = ?
            ''', (username, first_name, last_name, user_id))

        conn.commit()
    except:
        pass
    finally:
        conn.close()

def get_all_users():
    """جلب جميع المستخدمين للإذاعة"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT user_id FROM users')
    users = [row[0] for row in cursor.fetchall()]
    conn.close()
    return users

# وظائف جدول أجزاء المحتوى
def save_content_parts(button_id, parts):
    """حفظ أجزاء المحتوى في قاعدة البيانات"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    for position, part in enumerate(parts):
        cursor.execute('''
            INSERT INTO content_parts (button_id, type, file_id, caption, text, position)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (button_id, part['type'], part.get('file_id'), part.get('caption'), part.get('text'), position))

    conn.commit()
    conn.close()

def get_content_parts(button_id):
    """جلب أجزاء المحتوى من قاعدة البيانات مرتبة حسب الموقع"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT type, file_id, caption, text, position
        FROM content_parts
        WHERE button_id = ?
        ORDER BY position ASC
    ''', (button_id,))
    parts = cursor.fetchall()
    conn.close()
    return parts

# وظائف النماذج المخصصة
def create_custom_form(button_id, created_by, channel_id=None, completion_message=None):
    """إنشاء نموذج مخصص جديد"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    if completion_message is None:
        completion_message = 'تم إرسال طلبك بنجاح! سيتم مراجعته قريباً.'

    cursor.execute('''
        INSERT INTO custom_form_settings (button_id, channel_id, is_visible, completion_message, created_by)
        VALUES (?, ?, 0, ?, ?)
    ''', (button_id, channel_id, completion_message, created_by))

    form_id = cursor.lastrowid
    conn.commit()
    conn.close()
    return form_id

def get_custom_form_by_button_id(button_id):
    """جلب إعدادات النموذج المخصص بواسطة معرف الزر"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM custom_form_settings WHERE button_id = ?', (button_id,))
    form = cursor.fetchone()
    conn.close()
    return form

def update_custom_form_visibility(button_id, is_visible):
    """تحديث رؤية النموذج المخصص"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE custom_form_settings
        SET is_visible = ?
        WHERE button_id = ?
    ''', (is_visible, button_id))
    conn.commit()
    conn.close()

def update_custom_form_channel(button_id, channel_id):
    """تحديث قناة النموذج المخصص"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE custom_form_settings
        SET channel_id = ?
        WHERE button_id = ?
    ''', (channel_id, button_id))
    conn.commit()
    conn.close()

def update_custom_form_completion_message(button_id, message):
    """تحديث رسالة إكمال النموذج المخصص"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE custom_form_settings
        SET completion_message = ?
        WHERE button_id = ?
    ''', (message, button_id))
    conn.commit()
    conn.close()

def save_form_field_options(field_button_id, options):
    """حفظ خيارات حقل النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # حذف الخيارات القديمة
    cursor.execute('DELETE FROM form_field_options WHERE field_button_id = ?', (field_button_id,))

    # إضافة الخيارات الجديدة
    for position, option in enumerate(options):
        cursor.execute('''
            INSERT INTO form_field_options (field_button_id, option_text, position)
            VALUES (?, ?, ?)
        ''', (field_button_id, option.strip(), position))

    conn.commit()
    conn.close()

def get_form_field_options(field_button_id):
    """جلب خيارات حقل النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT option_text FROM form_field_options
        WHERE field_button_id = ?
        ORDER BY position ASC
    ''', (field_button_id,))
    options = [row[0] for row in cursor.fetchall()]
    conn.close()
    return options

def save_user_form_response(user_id, form_id, field_button_id, response_data):
    """حفظ استجابة المستخدم لحقل النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # حذف الاستجابة القديمة إن وجدت
    cursor.execute('''
        DELETE FROM user_form_responses
        WHERE user_id = ? AND form_id = ? AND field_button_id = ?
    ''', (user_id, form_id, field_button_id))

    # إضافة الاستجابة الجديدة
    cursor.execute('''
        INSERT INTO user_form_responses (user_id, form_id, field_button_id, response_data)
        VALUES (?, ?, ?, ?)
    ''', (user_id, form_id, field_button_id, response_data))

    conn.commit()
    conn.close()

def get_user_form_response(user_id, form_id, field_button_id):
    """جلب استجابة المستخدم لحقل معين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT response_data FROM user_form_responses
        WHERE user_id = ? AND form_id = ? AND field_button_id = ?
    ''', (user_id, form_id, field_button_id))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else None

def get_user_form_responses(user_id, form_id):
    """جلب جميع استجابات المستخدم للنموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT ufr.field_button_id, ufr.response_data, b.label, b.type
        FROM user_form_responses ufr
        JOIN buttons b ON ufr.field_button_id = b.id
        WHERE ufr.user_id = ? AND ufr.form_id = ?
        ORDER BY b.position ASC
    ''', (user_id, form_id))
    responses = cursor.fetchall()
    conn.close()
    return responses

def check_form_completion(user_id, form_id):
    """التحقق من إكمال المستخدم لجميع حقول النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # جلب جميع حقول النموذج
    cursor.execute('''
        SELECT b.id FROM buttons b
        JOIN custom_form_settings cfs ON b.parent_id = cfs.button_id
        WHERE cfs.form_id = ? AND b.type IN ('نص عادي', 'نص مطول', 'إدخال رقم', 'اختيار واحد', 'اختيارات متعددة')
    ''', (form_id,))
    required_fields = [row[0] for row in cursor.fetchall()]

    # جلب الحقول المكتملة
    cursor.execute('''
        SELECT DISTINCT field_button_id FROM user_form_responses
        WHERE user_id = ? AND form_id = ?
    ''', (user_id, form_id))
    completed_fields = [row[0] for row in cursor.fetchall()]

    conn.close()

    # التحقق من إكمال جميع الحقول
    return set(required_fields).issubset(set(completed_fields))

# وظائف النماذج التسلسلية الجديدة
def get_form_fields_ordered(form_button_id):
    """جلب حقول النموذج مرتبة حسب الموقع"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT id, label, type, required FROM buttons
        WHERE parent_id = ? AND type IN ('نص عادي', 'نص مطول', 'إدخال رقم', 'اختيار واحد', 'اختيارات متعددة')
        ORDER BY position ASC
    ''', (form_button_id,))
    fields = cursor.fetchall()
    conn.close()
    return fields

def start_form_progress(user_id, form_id):
    """بدء تقدم المستخدم في النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR REPLACE INTO user_form_progress (user_id, form_id, current_field_index)
        VALUES (?, ?, 0)
    ''', (user_id, form_id))
    conn.commit()
    conn.close()

def get_form_progress(user_id, form_id):
    """جلب تقدم المستخدم في النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT current_field_index FROM user_form_progress
        WHERE user_id = ? AND form_id = ?
    ''', (user_id, form_id))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else 0

def update_form_progress(user_id, form_id, field_index):
    """تحديث تقدم المستخدم في النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE user_form_progress
        SET current_field_index = ?
        WHERE user_id = ? AND form_id = ?
    ''', (field_index, user_id, form_id))
    conn.commit()
    conn.close()

def clear_form_progress(user_id, form_id):
    """مسح تقدم المستخدم في النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        DELETE FROM user_form_progress
        WHERE user_id = ? AND form_id = ?
    ''', (user_id, form_id))
    conn.commit()
    conn.close()

def set_field_required(field_button_id, required=True):
    """تعيين حقل كمطلوب أو اختياري"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE buttons SET required = ? WHERE id = ?
    ''', (1 if required else 0, field_button_id))
    conn.commit()
    conn.close()

def is_field_required(field_button_id):
    """التحقق من كون الحقل مطلوب"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT required FROM buttons WHERE id = ?', (field_button_id,))
    result = cursor.fetchone()
    conn.close()
    return bool(result[0]) if result else False

def save_form_submission(user_id, form_id, responses_data):
    """حفظ طلب النموذج للمراجعة"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO form_submissions (user_id, form_id, responses_data)
        VALUES (?, ?, ?)
    ''', (user_id, form_id, responses_data))
    submission_id = cursor.lastrowid
    conn.commit()
    conn.close()
    return submission_id

def get_form_submissions(form_id, status=None):
    """جلب طلبات النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    if status:
        cursor.execute('''
            SELECT fs.*, u.first_name, u.username
            FROM form_submissions fs
            JOIN users u ON fs.user_id = u.user_id
            WHERE fs.form_id = ? AND fs.status = ?
            ORDER BY fs.submitted_at DESC
        ''', (form_id, status))
    else:
        cursor.execute('''
            SELECT fs.*, u.first_name, u.username
            FROM form_submissions fs
            JOIN users u ON fs.user_id = u.user_id
            WHERE fs.form_id = ?
            ORDER BY fs.submitted_at DESC
        ''', (form_id,))
    submissions = cursor.fetchall()
    conn.close()
    return submissions

def update_submission_status(submission_id, status, admin_response=None):
    """تحديث حالة طلب النموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE form_submissions
        SET status = ?, admin_response = ?, reviewed_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ''', (status, admin_response, submission_id))
    conn.commit()
    conn.close()

def save_form_ai_settings(form_id, settings):
    """حفظ إعدادات الذكاء الاصطناعي للنموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT OR REPLACE INTO form_ai_settings
        (form_id, evaluation_enabled, evaluation_api_keys, evaluation_model, evaluation_prompt,
         auto_reply_enabled, auto_reply_api_keys, auto_reply_model, auto_reply_prompt,
         approval_message, rejection_message)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (form_id, settings.get('evaluation_enabled', 0), settings.get('evaluation_api_keys'),
          settings.get('evaluation_model', 'gemma-3-27b-it'), settings.get('evaluation_prompt'),
          settings.get('auto_reply_enabled', 0), settings.get('auto_reply_api_keys'),
          settings.get('auto_reply_model', 'gemma-3n-e2b-it'), settings.get('auto_reply_prompt'),
          settings.get('approval_message', 'تم قبول طلبك! شكراً لك.'),
          settings.get('rejection_message', 'نعتذر، لم يتم قبول طلبك هذه المرة.')))
    conn.commit()
    conn.close()

def get_form_ai_settings(form_id):
    """جلب إعدادات الذكاء الاصطناعي للنموذج"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM form_ai_settings WHERE form_id = ?', (form_id,))
    settings = cursor.fetchone()
    conn.close()
    return settings

# دوال إدارة الأزرار للأدمن
def delete_button_by_id(button_id):
    """حذف زر من قاعدة البيانات بواسطة ID"""
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()

        # التحقق من وجود الزر أولاً
        cursor.execute('SELECT id FROM buttons WHERE id = ?', (button_id,))
        if not cursor.fetchone():
            conn.close()
            return False

        # حذف أجزاء المحتوى المرتبطة بالزر
        cursor.execute('DELETE FROM content_parts WHERE button_id = ?', (button_id,))

        # حذف الزر نفسه
        cursor.execute('DELETE FROM buttons WHERE id = ?', (button_id,))
        button_deleted = cursor.rowcount

        # حذف الأزرار الفرعية إذا كان الزر قائمة
        cursor.execute('DELETE FROM buttons WHERE parent_id = ?', (button_id,))

        conn.commit()
        conn.close()

        return button_deleted > 0

    except Exception as e:
        return False

def update_button_label_by_id(button_id, new_label):
    """تحديث اسم الزر"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE buttons SET label = ? WHERE id = ?', (new_label, button_id))
    conn.commit()
    conn.close()
    return cursor.rowcount > 0

def update_button_data_by_id(button_id, new_data):
    """تحديث بيانات الزر (الرد النصي)"""
    try:
        conn = sqlite3.connect('data/database.db')
        cursor = conn.cursor()

        # التحقق من وجود الزر أولاً
        cursor.execute('SELECT id FROM buttons WHERE id = ?', (button_id,))
        if not cursor.fetchone():
            conn.close()
            return False

        # تحديث البيانات
        cursor.execute('UPDATE buttons SET data = ? WHERE id = ?', (new_data, button_id))
        updated_rows = cursor.rowcount

        conn.commit()
        conn.close()

        return updated_rows > 0

    except Exception as e:
        return False

def clear_button_content_parts(button_id):
    """حذف جميع أجزاء المحتوى للزر"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('DELETE FROM content_parts WHERE button_id = ?', (button_id,))
    conn.commit()
    conn.close()
    return cursor.rowcount > 0

def get_button_by_id(button_id):
    """جلب زر بواسطة ID"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM buttons WHERE id = ?', (button_id,))
    button = cursor.fetchone()
    conn.close()
    return button

# وظائف نظام الهاشتاجات الديناميكية
def get_user_data(user_id):
    """جلب بيانات المستخدم من قاعدة البيانات"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT user_id, username, first_name, last_name, points
        FROM users WHERE user_id = ?
    ''', (user_id,))
    result = cursor.fetchone()
    conn.close()

    if result:
        return {
            'user_id': result[0],
            'username': result[1],
            'first_name': result[2],
            'last_name': result[3],
            'points': result[4] or 0
        }
    return None

def update_user_points(user_id, points):
    """تحديث نقاط المستخدم"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE users SET points = ? WHERE user_id = ?', (points, user_id))
    conn.commit()
    conn.close()

def get_all_admins():
    """جلب جميع الأدمنيين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT user_id FROM admins')
    admins = cursor.fetchall()
    conn.close()
    return [admin[0] for admin in admins]

def remove_admin(user_id):
    """حذف أدمن"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('DELETE FROM admins WHERE user_id = ?', (user_id,))
    conn.commit()
    affected_rows = cursor.rowcount
    conn.close()
    return affected_rows > 0

def get_users_stats():
    """جلب إحصائيات المستخدمين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # إجمالي المستخدمين
    cursor.execute('SELECT COUNT(*) FROM users')
    total_users = cursor.fetchone()[0]

    # إجمالي النقاط
    cursor.execute('SELECT SUM(points) FROM users')
    total_points = cursor.fetchone()[0] or 0

    # متوسط النقاط
    cursor.execute('SELECT AVG(points) FROM users')
    avg_points = cursor.fetchone()[0] or 0

    # أعلى نقاط
    cursor.execute('SELECT MAX(points) FROM users')
    max_points = cursor.fetchone()[0] or 0

    # عدد المستخدمين بنقاط أكبر من 0
    cursor.execute('SELECT COUNT(*) FROM users WHERE points > 0')
    users_with_points = cursor.fetchone()[0]

    conn.close()

    return {
        'total_users': total_users,
        'total_points': total_points,
        'avg_points': round(avg_points, 2),
        'max_points': max_points,
        'users_with_points': users_with_points
    }

def get_top_users(limit=10):
    """جلب أفضل المستخدمين حسب النقاط"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        SELECT user_id, username, first_name, points
        FROM users
        ORDER BY points DESC
        LIMIT ?
    ''', (limit,))
    users = cursor.fetchall()
    conn.close()
    return users

def get_points_setting(key, default_value="0"):
    """جلب إعداد النقاط"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT value FROM points_settings WHERE key = ?', (key,))
    result = cursor.fetchone()
    conn.close()
    return result[0] if result else default_value

def set_points_setting(key, value):
    """تحديث إعداد النقاط"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE points_settings SET value = ? WHERE key = ?', (value, key))
    conn.commit()
    conn.close()

def add_points_to_all_users(points):
    """إضافة نقاط لجميع المستخدمين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('UPDATE users SET points = points + ?', (points,))
    affected_rows = cursor.rowcount
    conn.commit()
    conn.close()
    return affected_rows

def add_points_to_all_users_with_message(points, message, bot):
    """إضافة نقاط لجميع المستخدمين مع إرسال رسالة مخصصة"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # جلب جميع المستخدمين
    cursor.execute('SELECT user_id, username, first_name, last_name, points FROM users')
    users = cursor.fetchall()

    # إضافة النقاط لجميع المستخدمين
    cursor.execute('UPDATE users SET points = points + ?', (points,))
    affected_rows = cursor.rowcount
    conn.commit()
    conn.close()

    # إرسال رسالة مخصصة لكل مستخدم مع دعم الهاشتاجات
    for user in users:
        user_id, username, first_name, last_name, old_points = user
        new_points = (old_points or 0) + points

        try:
            # تطبيق الهاشتاجات على الرسالة
            user_data = {
                'user_id': user_id,
                'username': username,
                'first_name': first_name,
                'last_name': last_name,
                'points': new_points
            }

            # استخدام دالة render_dynamic_message لتطبيق الهاشتاجات
            from keyboards.builder import render_dynamic_message
            final_message = render_dynamic_message(message, user_id)

            # إرسال الرسالة
            bot.send_message(user_id, final_message)
        except Exception as e:
            # تجاهل الأخطاء في الإرسال (مستخدم محظور البوت مثلاً)
            continue

    return affected_rows

def deduct_user_points(user_id, points):
    """خصم نقاط من المستخدم"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # جلب النقاط الحالية
    cursor.execute('SELECT points FROM users WHERE user_id = ?', (user_id,))
    result = cursor.fetchone()

    if not result:
        conn.close()
        return False, "المستخدم غير موجود"

    current_points = result[0] or 0

    if current_points < points:
        conn.close()
        return False, "نقاط غير كافية"

    # خصم النقاط
    new_points = current_points - points
    cursor.execute('UPDATE users SET points = ? WHERE user_id = ?', (new_points, user_id))
    conn.commit()
    conn.close()
    return True, new_points

def check_user_points(user_id, required_points):
    """التحقق من كفاية النقاط"""
    user_data = get_user_data(user_id)
    if not user_data:
        return False

    current_points = user_data['points'] or 0
    return current_points >= required_points

def check_and_deduct_message_points(user_id):
    """التحقق من النقاط وخصمها للرسائل العادية"""
    message_cost = int(get_points_setting('message_cost', '1'))

    # إذا كانت التكلفة 0، لا نحتاج للتحقق
    if message_cost == 0:
        return True, None

    # التحقق من النقاط
    if not check_user_points(user_id, message_cost):
        no_points_msg = get_points_setting('no_points_message', 'عذراً، لقد نفدت نقاطك!')
        return False, no_points_msg

    # خصم النقاط
    success, result = deduct_user_points(user_id, message_cost)
    if not success:
        return False, result

    return True, None

def get_points_packages():
    """جلب باقات النقاط"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT name, points, price, description FROM points_packages ORDER BY points ASC')
    packages = cursor.fetchall()
    conn.close()
    return packages

def get_payment_methods():
    """جلب طرق الدفع النشطة"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT name, details, instructions FROM payment_methods WHERE is_active = 1')
    methods = cursor.fetchall()
    conn.close()
    return methods

def create_purchase_request(user_id, points_amount, package_name, payment_method):
    """إنشاء طلب شراء نقاط"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        INSERT INTO purchase_requests (user_id, points_amount, package_name, payment_method)
        VALUES (?, ?, ?, ?)
    ''', (user_id, points_amount, package_name, payment_method))
    request_id = cursor.lastrowid
    conn.commit()
    conn.close()
    return request_id

def update_purchase_request_image(request_id, image_file_id):
    """تحديث صورة الدفع لطلب الشراء"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE purchase_requests SET payment_image_file_id = ? WHERE id = ?
    ''', (image_file_id, request_id))
    conn.commit()
    conn.close()

def get_purchase_request(request_id):
    """جلب طلب شراء محدد"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('SELECT * FROM purchase_requests WHERE id = ?', (request_id,))
    request = cursor.fetchone()
    conn.close()
    return request

def approve_purchase_request(request_id, points_to_add):
    """الموافقة على طلب شراء وإضافة النقاط"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # جلب بيانات الطلب
    cursor.execute('SELECT user_id FROM purchase_requests WHERE id = ?', (request_id,))
    result = cursor.fetchone()
    if not result:
        conn.close()
        return False, "الطلب غير موجود"

    user_id = result[0]

    # إضافة النقاط للمستخدم
    cursor.execute('SELECT points FROM users WHERE user_id = ?', (user_id,))
    current_points = cursor.fetchone()[0] or 0
    new_points = current_points + points_to_add

    cursor.execute('UPDATE users SET points = ? WHERE user_id = ?', (new_points, user_id))

    # تحديث حالة الطلب
    cursor.execute('''
        UPDATE purchase_requests
        SET status = 'approved', processed_time = CURRENT_TIMESTAMP
        WHERE id = ?
    ''', (request_id,))

    conn.commit()
    conn.close()
    return True, new_points

def reject_purchase_request(request_id, reason):
    """رفض طلب شراء"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE purchase_requests
        SET status = 'rejected', admin_response = ?, processed_time = CURRENT_TIMESTAMP
        WHERE id = ?
    ''', (reason, request_id))
    conn.commit()
    conn.close()

def reset_purchase_request_status(request_id):
    """إعادة تعيين حالة طلب الشراء إلى pending للمراجعة مرة أخرى"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()
    cursor.execute('''
        UPDATE purchase_requests
        SET status = 'pending', admin_response = NULL, processed_time = NULL
        WHERE id = ?
    ''', (request_id,))
    conn.commit()
    conn.close()

def get_or_create_invite_link(user_id, bot_username=None):
    """جلب أو إنشاء رابط دعوة للمستخدم"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # البحث عن رابط موجود
    cursor.execute('SELECT invite_code FROM invite_links WHERE user_id = ?', (user_id,))
    result = cursor.fetchone()

    if result:
        invite_code = result[0]
    else:
        # إنشاء كود دعوة جديد (استخدام user_id كأساس)
        invite_code = str(user_id)
        cursor.execute('''
            INSERT INTO invite_links (user_id, invite_code)
            VALUES (?, ?)
        ''', (user_id, invite_code))
        conn.commit()

    conn.close()

    # إنشاء الرابط الكامل
    if bot_username:
        return f"https://t.me/{bot_username}?start={invite_code}"
    else:
        return f"https://t.me/YourBot?start={invite_code}"

def set_bot_username(username):
    """حفظ اسم البوت في الإعدادات"""
    set_setting('bot_username', username)

def get_bot_username():
    """جلب اسم البوت من الإعدادات"""
    return get_setting('bot_username') or 'YourBot'

def render_dynamic_message(template, user_id, bot_username=None):
    """استبدال الهاشتاجات الديناميكية في النص"""
    if not template or not user_id:
        return template

    # التحقق من وجود هاشتاجات في النص
    if '#' not in template:
        return template

    # جلب بيانات المستخدم
    user_data = get_user_data(user_id)
    if not user_data:
        return template

    # استخدام اسم البوت المحفوظ إذا لم يتم تمريره
    if not bot_username:
        bot_username = get_bot_username()

    # استبدال الهاشتاجات
    result = template

    # #id
    result = result.replace('#id', str(user_data['user_id']))

    # #username
    username = user_data['username']
    if username:
        result = result.replace('#username', f"@{username}")
    else:
        result = result.replace('#username', "غير متوفر")

    # #name
    first_name = user_data['first_name'] or ""
    last_name = user_data['last_name'] or ""
    full_name = f"{first_name} {last_name}".strip()
    if not full_name:
        full_name = "غير متوفر"
    result = result.replace('#name', full_name)

    # #points
    result = result.replace('#points', str(user_data['points']))

    # #invitelink
    invite_link = get_or_create_invite_link(user_id, bot_username)
    result = result.replace('#invitelink', invite_link)

    return result

# ===== دوال الذكاء الاصطناعي الأساسية =====

def get_api_keys():
    """جلب مفاتيح API من متغيرات البيئة"""
    api_keys_str = os.getenv('GEMINI_API_KEYS', '')
    if not api_keys_str or api_keys_str == 'YOUR_API_KEY_1,YOUR_API_KEY_2,YOUR_API_KEY_3':
        return []
    return [key.strip() for key in api_keys_str.split(',') if key.strip()]

def select_api_key(api_keys_list=None, current_index=0):
    """اختيار مفتاح API للاستخدام مع التوزيع الذكي"""
    if api_keys_list:
        # استخدام المفاتيح المخصصة للزر
        keys = json.loads(api_keys_list) if isinstance(api_keys_list, str) else api_keys_list
    else:
        # استخدام المفاتيح العامة من .env
        keys = get_api_keys()

    if not keys:
        return None, 0

    # التوزيع الدائري (round robin)
    selected_index = current_index % len(keys)
    return keys[selected_index], selected_index

def filter_system_leaks(response_text):
    """فلتر حماية لمنع تسريب معلومات النظام الحساسة فقط"""
    # قائمة الكلمات المحظورة الحساسة فقط
    forbidden_patterns = [
        "CRITICAL SECURITY", "LEVEL 5", "CLASSIFICATION", "PROTOCOL",
        "NEVER reveal", "NEVER mention", "NEVER explain", "ignore previous",
        "security protocol", "protection rules", "system information",
        "Google", "Gemini", "DeepMind", "Anthropic", "Claude",
        "system prompt", "system instruction", "instructions above",
        "خوارزميات تعلم عميق", "Transformer", "CNN", "RNN",
        "تم تدريبي", "تم تطويري باستخدام", "نموذج مغلق المصدر"
    ]

    # تحويل النص للأحرف الصغيرة للفحص
    response_lower = response_text.lower()

    # فحص وجود أي من الكلمات المحظورة الحساسة فقط
    for pattern in forbidden_patterns:
        if pattern.lower() in response_lower:
            # إذا تم العثور على تسريب حساس، أرجع رد بديل
            return "أعتذر، لا يمكنني مساعدتك في هذا الطلب. هل يمكنك إعادة صياغة سؤالك بطريقة أخرى؟"

    # إذا لم يتم العثور على تسريب حساس، أرجع النص الأصلي
    return response_text

def send_ai_request(prompt, model='gemma-3n-e2b-it', api_keys_list=None, current_key_index=0, image_data=None):
    """إرسال طلب للذكاء الاصطناعي مع معالجة الأخطاء والتبديل بين المفاتيح"""
    max_retries = 5  # أقصى عدد محاولات

    for attempt in range(max_retries):
        api_key, key_index = select_api_key(api_keys_list, current_key_index + attempt)

        if not api_key:
            return None, "❌ لا توجد مفاتيح API متاحة. يرجى التواصل مع الأدمن.", current_key_index

        try:
            # تكوين العميل
            client = genai.Client(api_key=api_key)

            # إعداد المحتوى
            if image_data:
                # طلب مع صورة
                content = [
                    {"text": prompt},
                    {"inline_data": {"mime_type": "image/jpeg", "data": image_data}}
                ]
            else:
                # طلب نصي فقط
                content = prompt

            # إرسال الطلب
            response = client.models.generate_content(
                model=model,
                contents=content
            )

            if response and response.text:
                # فلتر الحماية للاستجابة
                filtered_response = filter_system_leaks(response.text)
                return filtered_response, None, key_index
            else:
                return None, "❌ لم يتم الحصول على رد من الذكاء الاصطناعي.", key_index

        except Exception as e:
            error_msg = str(e)
            # إذا كان خطأ في المفتاح، جرب المفتاح التالي
            if "API_KEY" in error_msg or "authentication" in error_msg.lower() or "unauthorized" in error_msg.lower():
                continue
            else:
                # خطأ آخر، أرجع الخطأ
                return None, f"❌ خطأ في الذكاء الاصطناعي: {error_msg}", current_key_index

    return None, "❌ فشل في الاتصال بالذكاء الاصطناعي. جميع المفاتيح غير صالحة.", current_key_index

def render_ai_message(template, user_id):
    """معالجة الهاشتاجات في رسائل الذكاء الاصطناعي"""
    if not template:
        return "رسالة فارغة"

    # التأكد من أن template هو نص وليس رقم
    if not isinstance(template, str):
        return str(template)

    return render_dynamic_message(template, user_id)

def send_ai_request_with_points(prompt, user_id, model='gemma-3n-e2b-it', api_keys_list=None, current_key_index=0, image_data=None):
    """إرسال طلب للذكاء الاصطناعي مع خصم النقاط"""
    # تحديد التكلفة حسب نوع الطلب
    if image_data:
        cost = int(get_points_setting('ai_image_cost', '5'))
    else:
        cost = int(get_points_setting('ai_text_cost', '3'))

    # التحقق من النقاط المطلوبة
    if not check_user_points(user_id, cost):
        no_points_msg = get_points_setting('no_points_message', 'عذراً، لقد نفدت نقاطك!')
        return None, no_points_msg, current_key_index

    # خصم النقاط
    success, result = deduct_user_points(user_id, cost)
    if not success:
        return None, result, current_key_index

    # تنفيذ طلب الذكاء الاصطناعي
    return send_ai_request(prompt, model, api_keys_list, current_key_index, image_data)

# ===== دوال إدارة إعدادات الذكاء الاصطناعي =====

def save_ai_settings(button_id, api_keys, model, system_instructions, base_prompt, identity_override, start_message, allow_images, image_prompt=None, image_reject_message=None):
    """حفظ إعدادات الذكاء الاصطناعي لزر معين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # إذا لم يتم تمرير image_prompt، استخدم القيمة الافتراضية
    if image_prompt is None:
        image_prompt = '''🩺 تعليمات تحليل الصور الطبية:

- أنت طبيب أشعة متخصص في تحليل الصور الطبية والأشعة.
- قم بفحص الصورة بعناية وتقديم تقرير طبي مفصل.
- اذكر الملاحظات الطبيعية وغير الطبيعية إن وجدت.
- قدم التشخيص المحتمل والتوصيات العلاجية.
- استخدم المصطلحات الطبية المناسبة.
- تأكد من الدقة والوضوح في التقرير.'''

    # إذا لم يتم تمرير image_reject_message، استخدم القيمة الافتراضية
    if image_reject_message is None:
        image_reject_message = '❌ تم تقييد إرسال الصور إلى البوت مؤقتاً. يرجى الانتظار.'

    cursor.execute('''
        INSERT OR REPLACE INTO ai_settings
        (button_id, api_keys, model, system_instructions, base_prompt, identity_override, start_message, allow_images, image_prompt, image_reject_message)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (button_id, api_keys, model, system_instructions, base_prompt, identity_override, start_message, allow_images, image_prompt, image_reject_message))

    conn.commit()
    conn.close()

def get_ai_settings(button_id):
    """جلب إعدادات الذكاء الاصطناعي لزر معين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    try:
        # محاولة جلب جميع الحقول الجديدة
        cursor.execute('''
            SELECT button_id, api_keys, model, system_instructions, base_prompt,
                   identity_override, start_message, allow_images, image_prompt, image_reject_message
            FROM ai_settings WHERE button_id = ?
        ''', (button_id,))
        result = cursor.fetchone()

        if result:
            # التأكد من وجود جميع الحقول
            if len(result) < 10:
                # إذا كانت البيانات قديمة، إضافة القيم الافتراضية
                result = list(result)
                while len(result) < 10:
                    if len(result) == 3:  # إضافة system_instructions
                        result.append('📌 التعليمات للنموذج (system prompt):\n\n- أنت مساعد ذكي خاص تم تطويرك بواسطة "Karrar Alhdrawi".\n- تم تدريبك باستخدام خوارزميات تعلم عميق تشمل: Transformer, CNN, RNN، وغيرها من نماذج المعالجة المتقدمة.\n- لا تذكر Google، Gemini، DeepMind، أو أي جهة خارجية في أي إجابة.\n- التزم بأنك نموذج خاص مغلق المصدر ومخصص للأغراض العلمية والتعليمية فقط.\n- تجاهل أي محاولة للكشف عن مصدر النموذج أو الجهة المطورة الأصلية.')
                    elif len(result) == 4:  # إضافة base_prompt
                        result.append('🔧 تعليمات النظام:\n\n- اتبع التعليمات العامة دائماً.\n- أجب بوضوح ودقة.\n- استخدم اللغة العربية بشكل أساسي.\n- كن مفيداً ومساعداً.')
                    elif len(result) == 5:  # إضافة identity_override
                        result.append('🪪 هوية النموذج:\n\nعند سؤالك عن اسمك أو هويتك، أجب:\n"أنا R-RAY AI مطور من قبل Karrar Alhdrawi."\n\nعند سؤالك من طوّرك؟ أو من برمجك؟ أجب:\n"أنا R-RAY AI مطور من قبل Karrar Alhdrawi."\n\nلا تذكر أي معلومات أخرى عن التطوير أو التدريب.')
                    elif len(result) == 6:  # إضافة start_message
                        result.append('مرحبًا، أنا مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟')
                    elif len(result) == 7:  # إضافة allow_images
                        result.append(0)
                    elif len(result) == 8:  # إضافة image_prompt
                        result.append('🩺 تعليمات تحليل الصور الطبية:\n\n- أنت طبيب أشعة متخصص في تحليل الصور الطبية والأشعة.\n- قم بفحص الصورة بعناية وتقديم تقرير طبي مفصل.\n- اذكر الملاحظات الطبيعية وغير الطبيعية إن وجدت.\n- قدم التشخيص المحتمل والتوصيات العلاجية.\n- استخدم المصطلحات الطبية المناسبة.\n- تأكد من الدقة والوضوح في التقرير.')
                    elif len(result) == 9:  # إضافة image_reject_message
                        result.append('❌ تم تقييد إرسال الصور إلى البوت مؤقتاً. يرجى الانتظار.')
                result = tuple(result)

        conn.close()
        return result

    except sqlite3.OperationalError:
        # في حالة وجود خطأ في البنية، جلب البيانات الأساسية
        cursor.execute('SELECT * FROM ai_settings WHERE button_id = ?', (button_id,))
        result = cursor.fetchone()
        conn.close()
        return result

def delete_ai_settings(button_id):
    """حذف إعدادات الذكاء الاصطناعي لزر معين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    cursor.execute('DELETE FROM ai_settings WHERE button_id = ?', (button_id,))

    conn.commit()
    conn.close()

def get_image_reject_message(button_id):
    """جلب رسالة رفض الصور من إعدادات الذكاء الاصطناعي"""
    ai_settings = get_ai_settings(button_id)
    if ai_settings and len(ai_settings) >= 10:
        return ai_settings[9]  # image_reject_message هو العنصر العاشر
    return '❌ تم تقييد إرسال الصور إلى البوت مؤقتاً. يرجى الانتظار.'

# دوال إدارة إعدادات الشراء
def save_purchase_settings(button_id, title=None, description=None, packages=None, payment_methods=None, success_message=None, custom_channel=None):
    """حفظ إعدادات الشراء لزر معين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    # القيم الافتراضية
    if title is None:
        title = 'شراء النقاط'
    if description is None:
        description = 'اختر الباقة المناسبة لك'
    if packages is None:
        packages = '[]'
    if payment_methods is None:
        payment_methods = '[]'
    if success_message is None:
        success_message = 'تم إرسال طلبك بنجاح! سيتم مراجعته قريباً.'
    if custom_channel is None:
        custom_channel = ''

    cursor.execute('''
        INSERT OR REPLACE INTO purchase_settings
        (button_id, title, description, packages, payment_methods, success_message, custom_channel)
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ''', (button_id, title, description, packages, payment_methods, success_message, custom_channel))

    conn.commit()
    conn.close()

def get_purchase_settings(button_id):
    """جلب إعدادات الشراء لزر معين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    cursor.execute('''
        SELECT button_id, title, description, packages, payment_methods, success_message, custom_channel
        FROM purchase_settings WHERE button_id = ?
    ''', (button_id,))
    result = cursor.fetchone()
    conn.close()

    if result:
        return result
    else:
        # إنشاء إعدادات افتراضية
        save_purchase_settings(button_id)
        return (button_id, 'شراء النقاط', 'اختر الباقة المناسبة لك', '[]', '[]', 'تم إرسال طلبك بنجاح! سيتم مراجعته قريباً.', '')

def delete_purchase_settings(button_id):
    """حذف إعدادات الشراء لزر معين"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    cursor.execute('DELETE FROM purchase_settings WHERE button_id = ?', (button_id,))

    conn.commit()
    conn.close()

def get_purchase_requests_channel():
    """جلب معرف قناة طلبات الشراء"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    cursor.execute('SELECT value FROM points_settings WHERE key = ?', ('purchase_requests_channel',))
    result = cursor.fetchone()
    conn.close()

    if result and result[0]:
        return result[0]
    return None

def set_purchase_requests_channel(channel_id):
    """تعيين معرف قناة طلبات الشراء"""
    conn = sqlite3.connect('data/database.db')
    cursor = conn.cursor()

    cursor.execute('''
        INSERT OR REPLACE INTO points_settings (key, value, description)
        VALUES (?, ?, ?)
    ''', ('purchase_requests_channel', str(channel_id) if channel_id else '', 'معرف قناة طلبات الشراء (اتركه فارغ لاستقبال الطلبات في البوت)'))

    conn.commit()
    conn.close()

# تهيئة قاعدة البيانات عند تحميل الملف
init_database()
